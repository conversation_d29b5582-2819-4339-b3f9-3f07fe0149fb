'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ALL_TEST_SITES, getRecommendedSites, getSitesByCategory, getAllSubcategories, getRecommendedRegionalSites, getSitesByTier, getSitesByRegion, getAllRegions, TestSite as ConfigTestSite } from '@/config/testSites';
import LatencyChart from '@/components/LatencyChart';

// 定义ping平台接口
interface PingPlatform {
  id: string;
  name: string;
  description: string;
  available: boolean;
  apiEndpoint?: string;
  color: string;
  icon: string;
}

// 定义测试结果接口
interface PingResult {
  platform: string;
  latency: number | null;
  status: 'pending' | 'success' | 'error' | 'timeout';
  error?: string;
  timestamp?: string;
}

// 使用配置文件中的TestSite接口
type TestSite = ConfigTestSite;

// 检测是否运行在海外环境 (Vercel等)
const isOverseasEnvironment = () => {
  // 检查环境变量
  if (process.env.NEXT_PUBLIC_IS_OVERSEAS === 'true') {
    return true;
  }

  // 检查是否在 Vercel 环境
  if (process.env.VERCEL === '1') {
    return true;
  }

  // 默认返回 false (国内环境)
  return false;

  // 原始检测逻辑（已禁用）
  // if (typeof window !== 'undefined') {
  //   return window.location.hostname.includes('vercel.app') ||
  //          window.location.hostname.includes('netlify.app') ||
  //          window.location.hostname.includes('vercel.com') ||
  //          window.location.hostname.includes('wobshare.us.kg') ||
  //          process.env.VERCEL === '1';
  // }
  // return process.env.VERCEL === '1' || process.env.NETLIFY === 'true' || process.env.NODE_ENV === 'production';
};

// 被墙平台列表 (在海外环境中这些平台可用)
const BLOCKED_PLATFORMS = [
  'globalping', 'keycdn', 'pingdom', 'gtmetrix', 'uptrends', 'dotcom-tools',
  'site24x7', 'host-tracker', 'whatismyip', 'network-tools', 'ping-eu',
  'just-ping', 'ca-app-synthetic', 'monitis', 'alertsite', 'keynote',
  'gomez', 'neustar', 'cedexis', 'thousandeyes', 'catchpoint', 'broadband-now',
  'speedof-me', 'testmy-net', 'pingplotter', 'prtg', 'solarwinds', 'nagios',
  'zabbix', 'icinga', 'observium', 'librenms', 'pandora-fms', 'cacti',
  'ping-asia', 'ping-america', 'ping-africa', 'oceania-ping', 'fastly',
  'maxcdn', 'bunnycdn', 'stackpath', 'youtube-ping', 'twitch-ping',
  'discord-ping', 'telegram-ping', 'shopify-ping', 'woocommerce-ping',
  'gitlab-ping', 'bitbucket-ping', 'gmail-ping', 'outlook-ping',
  'duckduckgo-ping', 'yandex-ping', 'reuters-ping', 'ap-news-ping',
  'paypal-ping', 'stripe-ping', 'coursera-ping', 'edx-ping', 'dropbox-ping',
  'onedrive-ping', 'amazon-ping', 'ebay-ping', 'uber-ping', 'google-maps-ping',
  'spotify-ping', 'apple-music-ping', 'netflix-ping', 'hulu-ping',
  'slack-ping', 'teams-ping', 'zoom-ping', 'facebook-ping', 'twitter-ping',
  'instagram-ping', 'linkedin-ping', 'tiktok-ping', 'github-pages-ping',
  'netlify-ping', 'heroku-ping', 'railway-ping', 'render-ping',
  'amazon-cloudfront', 'azure-cdn', 'google-cloud-cdn', 'keycdn-ping',
  'binance-ping', 'coinbase-ping', 'epic-games-ping', 'origin-ping',
  'uplay-ping', 'battlenet-ping', 'stackoverflow-ping', 'codepen-ping',
  'jsfiddle-ping', 'replit-ping', 'codesandbox-ping', 'salesforce-ping',
  'oracle-ping', 'sap-ping', 'ibm-ping', 'apple-ping', 'microsoft-ping',
  'google-ping', 'meta-ping', 'tesla-ping', 'wish-ping', 'etsy-ping',
  'canva-ping', 'figma-ping', 'notion-ping', 'airtable-ping'
];

// 获取平台可用性
const getPlatformAvailability = (platformId: string, defaultAvailable: boolean) => {
  if (BLOCKED_PLATFORMS.includes(platformId)) {
    return isOverseasEnvironment();
  }
  return defaultAvailable;
};

// 可用的ping平台
const PING_PLATFORMS: PingPlatform[] = [
  // 自有平台已移除

  // 国内平台
  {
    id: 'itdog',
    name: 'ITDOG.CN',
    description: '中国网络专家，多运营商测试',
    available: true,
    color: 'bg-blue-600',
    icon: '🐕'
  },
  {
    id: '17ce',
    name: '17CE.COM',
    description: '国内知名测速平台',
    available: true,
    color: 'bg-green-600',
    icon: '🚀'
  },
  {
    id: 'boce',
    name: '拨测 (BOCE)',
    description: '域名检测，网站测速',
    available: true,
    color: 'bg-purple-600',
    icon: '📊'
  },
  {
    id: 'chinaz',
    name: '站长工具',
    description: '站长之家ping测试工具',
    available: true,
    color: 'bg-indigo-600',
    icon: '🔧'
  },
  {
    id: 'webkaka',
    name: 'WebKaKa',
    description: '卡卡网网站测速',
    available: true,
    color: 'bg-cyan-600',
    icon: '🌐'
  },
  {
    id: 'ce8',
    name: 'CE8.CN',
    description: '测速网多节点测试',
    available: true,
    color: 'bg-teal-600',
    icon: '📡'
  },
  {
    id: 'ipip',
    name: 'IPIP.NET',
    description: 'IP地理位置和网络测试',
    available: true,
    color: 'bg-emerald-600',
    icon: '🗺️'
  },
  {
    id: 'ping-pe',
    name: 'Ping.pe',
    description: '全球ping测试工具',
    available: true,
    color: 'bg-lime-600',
    icon: '🌏'
  },

  // 国外平台（部分被墙）
  {
    id: 'globalping',
    name: 'Globalping.io',
    description: '全球200+节点专业网络测试',
    available: getPlatformAvailability('globalping', false),
    color: 'bg-red-600',
    icon: '🌍'
  },
  {
    id: 'keycdn',
    name: 'KeyCDN Tools',
    description: 'CDN性能测试，网站速度分析',
    available: getPlatformAvailability('keycdn', false),
    color: 'bg-orange-600',
    icon: '🔑'
  },
  {
    id: 'pingdom',
    name: 'Pingdom Tools',
    description: 'Pingdom网站速度测试',
    available: getPlatformAvailability('pingdom', false),
    color: 'bg-yellow-600',
    icon: '⏱️'
  },
  {
    id: 'gtmetrix',
    name: 'GTmetrix',
    description: '网站性能分析工具',
    available: getPlatformAvailability('gtmetrix', false),
    color: 'bg-amber-600',
    icon: '📈'
  },
  {
    id: 'uptrends',
    name: 'Uptrends',
    description: '全球40+节点网站监控',
    available: getPlatformAvailability('uptrends', false),
    color: 'bg-rose-600',
    icon: '📊'
  },
  {
    id: 'dotcom-tools',
    name: 'Dotcom-Tools',
    description: '免费网站速度测试工具',
    available: getPlatformAvailability('dotcom-tools', false),
    color: 'bg-pink-600',
    icon: '🛠️'
  },
  {
    id: 'site24x7',
    name: 'Site24x7',
    description: '企业级网站监控平台',
    available: getPlatformAvailability('site24x7', false),
    color: 'bg-fuchsia-600',
    icon: '🏢'
  },
  {
    id: 'host-tracker',
    name: 'Host-Tracker',
    description: '60+全球节点监控',
    available: getPlatformAvailability('host-tracker', false),
    color: 'bg-violet-600',
    icon: '🎯'
  },

  // CDN和云服务商测试
  {
    id: 'cloudflare',
    name: 'Cloudflare Speed',
    description: 'Cloudflare网络测试',
    available: true,
    color: 'bg-orange-500',
    icon: '☁️'
  },
  {
    id: 'aws-ping',
    name: 'AWS CloudPing',
    description: 'AWS全球区域延迟测试',
    available: true,
    color: 'bg-yellow-500',
    icon: '🚀'
  },
  {
    id: 'azure-ping',
    name: 'Azure Speed',
    description: 'Azure全球数据中心测试',
    available: true,
    color: 'bg-blue-500',
    icon: '🔷'
  },
  {
    id: 'gcp-ping',
    name: 'GCP Network',
    description: 'Google Cloud网络测试',
    available: true,
    color: 'bg-red-500',
    icon: '🌐'
  },
  {
    id: 'alicloud',
    name: '阿里云测速',
    description: '阿里云全球节点测试',
    available: true,
    color: 'bg-orange-400',
    icon: '☁️'
  },
  {
    id: 'tencent-cloud',
    name: '腾讯云测速',
    description: '腾讯云全球加速测试',
    available: true,
    color: 'bg-blue-400',
    icon: '🐧'
  },
  {
    id: 'huawei-cloud',
    name: '华为云测速',
    description: '华为云全球节点测试',
    available: true,
    color: 'bg-red-400',
    icon: '📱'
  },

  // 运营商和网络服务
  {
    id: 'speedtest',
    name: 'Speedtest.net',
    description: 'Ookla全球测速网络',
    available: true,
    color: 'bg-green-500',
    icon: '⚡'
  },
  {
    id: 'fast-com',
    name: 'Fast.com',
    description: 'Netflix提供的测速服务',
    available: true,
    color: 'bg-red-500',
    icon: '🎬'
  },
  {
    id: 'librespeed',
    name: 'LibreSpeed',
    description: '开源网络测速工具',
    available: true,
    color: 'bg-green-400',
    icon: '🆓'
  },

  // 更多国内平台
  {
    id: 'tool-lu',
    name: 'Tool.lu',
    description: '在线工具集合ping测试',
    available: true,
    color: 'bg-indigo-500',
    icon: '🔨'
  },
  {
    id: 'ip138',
    name: 'IP138.com',
    description: 'IP查询和网络工具',
    available: true,
    color: 'bg-purple-500',
    icon: '🔍'
  },
  {
    id: 'ip-cn',
    name: 'IP.CN',
    description: 'IP地址查询和ping测试',
    available: true,
    color: 'bg-cyan-500',
    icon: '🌐'
  },
  {
    id: 'linkwan',
    name: 'LinkWan',
    description: '网络连通性测试工具',
    available: true,
    color: 'bg-teal-500',
    icon: '🔗'
  },
  {
    id: 'netsh',
    name: 'Netsh.org',
    description: '网络诊断工具集',
    available: true,
    color: 'bg-emerald-500',
    icon: '🩺'
  },
  {
    id: 'ping-chinaz',
    name: 'Ping.ChinaZ',
    description: '站长之家专业ping工具',
    available: true,
    color: 'bg-lime-500',
    icon: '📊'
  },
  {
    id: 'webluker',
    name: 'WebLuker',
    description: '网站性能监测平台',
    available: true,
    color: 'bg-yellow-500',
    icon: '👁️'
  },
  {
    id: 'mmtrix',
    name: 'MMTrix',
    description: '多媒体网络测试',
    available: true,
    color: 'bg-amber-500',
    icon: '📱'
  },
  {
    id: 'netspeedtestmaster',
    name: 'NetSpeedTest',
    description: '网络速度测试大师',
    available: true,
    color: 'bg-orange-400',
    icon: '🚄'
  },
  {
    id: 'speedcn',
    name: 'Speed.cn',
    description: '测速网专业版',
    available: true,
    color: 'bg-red-400',
    icon: '🏃'
  },

  // 更多国外平台
  {
    id: 'whatismyip',
    name: 'WhatIsMyIP',
    description: 'IP查询和网络工具',
    available: getPlatformAvailability('whatismyip', false),
    color: 'bg-slate-600',
    icon: '🌍'
  },
  {
    id: 'network-tools',
    name: 'Network-Tools',
    description: '在线网络工具集合',
    available: getPlatformAvailability('network-tools', false),
    color: 'bg-gray-600',
    icon: '🛠️'
  },
  {
    id: 'ping-eu',
    name: 'Ping.eu',
    description: '欧洲ping测试服务',
    available: getPlatformAvailability('ping-eu', false),
    color: 'bg-zinc-600',
    icon: '🇪🇺'
  },
  {
    id: 'just-ping',
    name: 'Just-Ping',
    description: '简单全球ping测试',
    available: getPlatformAvailability('just-ping', false),
    color: 'bg-neutral-600',
    icon: '📍'
  },
  {
    id: 'ca-app-synthetic',
    name: 'CA App Synthetic',
    description: 'CA应用性能监控',
    available: getPlatformAvailability('ca-app-synthetic', false),
    color: 'bg-stone-600',
    icon: '📈'
  },
  {
    id: 'monitis',
    name: 'Monitis',
    description: '云监控和性能测试',
    available: getPlatformAvailability('monitis', false),
    color: 'bg-red-700',
    icon: '☁️'
  },
  {
    id: 'alertsite',
    name: 'AlertSite',
    description: '网站性能监控服务',
    available: getPlatformAvailability('alertsite', false),
    color: 'bg-orange-700',
    icon: '🚨'
  },
  {
    id: 'keynote',
    name: 'Keynote Systems',
    description: '数字体验测试平台',
    available: getPlatformAvailability('keynote', false),
    color: 'bg-yellow-700',
    icon: '🎯'
  },
  {
    id: 'gomez',
    name: 'Gomez Networks',
    description: '真实用户监控',
    available: getPlatformAvailability('gomez', false),
    color: 'bg-green-700',
    icon: '👥'
  },
  {
    id: 'neustar',
    name: 'Neustar UltraDNS',
    description: 'DNS和网络性能',
    available: getPlatformAvailability('neustar', false),
    color: 'bg-blue-700',
    icon: '🌐'
  },
  {
    id: 'cedexis',
    name: 'Cedexis Radar',
    description: '全球网络性能数据',
    available: getPlatformAvailability('cedexis', false),
    color: 'bg-indigo-700',
    icon: '📡'
  },
  {
    id: 'thousandeyes',
    name: 'ThousandEyes',
    description: '网络智能平台',
    available: getPlatformAvailability('thousandeyes', false),
    color: 'bg-purple-700',
    icon: '👁️'
  },
  {
    id: 'catchpoint',
    name: 'Catchpoint',
    description: '数字体验监控',
    available: getPlatformAvailability('catchpoint', false),
    color: 'bg-pink-700',
    icon: '🎣'
  },

  // 运营商和ISP测试
  {
    id: 'china-telecom',
    name: '中国电信测速',
    description: '电信官方网络测试',
    available: true,
    color: 'bg-blue-600',
    icon: '📞'
  },
  {
    id: 'china-unicom',
    name: '中国联通测速',
    description: '联通官方网络测试',
    available: true,
    color: 'bg-red-600',
    icon: '📱'
  },
  {
    id: 'china-mobile',
    name: '中国移动测速',
    description: '移动官方网络测试',
    available: true,
    color: 'bg-green-600',
    icon: '📲'
  },
  {
    id: 'broadband-now',
    name: 'BroadbandNow',
    description: '宽带速度测试',
    available: getPlatformAvailability('broadband-now', false),
    color: 'bg-cyan-700',
    icon: '📶'
  },
  {
    id: 'speedof-me',
    name: 'SpeedOf.Me',
    description: 'HTML5速度测试',
    available: getPlatformAvailability('speedof-me', false),
    color: 'bg-teal-700',
    icon: '🌊'
  },
  {
    id: 'testmy-net',
    name: 'TestMy.net',
    description: '独立速度测试',
    available: getPlatformAvailability('testmy-net', false),
    color: 'bg-emerald-700',
    icon: '🧪'
  },

  // CDN和边缘计算
  {
    id: 'jsdelivr',
    name: 'jsDelivr CDN',
    description: 'jsDelivr CDN性能测试',
    available: true,
    color: 'bg-orange-500',
    icon: '📦'
  },
  {
    id: 'unpkg',
    name: 'UNPKG CDN',
    description: 'UNPKG CDN速度测试',
    available: true,
    color: 'bg-yellow-600',
    icon: '📚'
  },
  {
    id: 'cdnjs',
    name: 'cdnjs',
    description: 'cdnjs CDN性能测试',
    available: true,
    color: 'bg-amber-600',
    icon: '⚡'
  },
  {
    id: 'bootcdn',
    name: 'BootCDN',
    description: 'Bootstrap中文网CDN',
    available: true,
    color: 'bg-purple-400',
    icon: '🥾'
  },
  {
    id: 'staticfile',
    name: '七牛云CDN',
    description: '七牛云静态资源CDN',
    available: true,
    color: 'bg-blue-400',
    icon: '☁️'
  },
  {
    id: 'baidu-cdn',
    name: '百度CDN',
    description: '百度静态资源CDN',
    available: true,
    color: 'bg-red-400',
    icon: '🔍'
  },

  // 专业网络工具
  {
    id: 'mtr-sh',
    name: 'MTR.sh',
    description: '在线MTR网络诊断',
    available: true,
    color: 'bg-gray-500',
    icon: '🛣️'
  },
  {
    id: 'traceroute-online',
    name: 'Traceroute Online',
    description: '在线路由跟踪工具',
    available: true,
    color: 'bg-slate-500',
    icon: '🗺️'
  },
  {
    id: 'nslookup-io',
    name: 'NSLookup.io',
    description: 'DNS查询和网络诊断',
    available: true,
    color: 'bg-zinc-500',
    icon: '🔎'
  },
  {
    id: 'whois-net',
    name: 'Whois.net',
    description: '域名信息和网络测试',
    available: true,
    color: 'bg-neutral-500',
    icon: '📋'
  },
  {
    id: 'dig-web',
    name: 'Dig WebInterface',
    description: '在线DNS挖掘工具',
    available: true,
    color: 'bg-stone-500',
    icon: '⛏️'
  },

  // 更多国内平台 - 第三波扩展
  {
    id: 'ip-tool',
    name: 'IP-Tool.cn',
    description: 'IP工具网络测试',
    available: true,
    color: 'bg-rose-400',
    icon: '🔧'
  },
  {
    id: 'speedtest-cn',
    name: 'SpeedTest.cn',
    description: '中国测速网',
    available: true,
    color: 'bg-pink-400',
    icon: '🏎️'
  },
  {
    id: 'netspeed-cc',
    name: 'NetSpeed.cc',
    description: '网速测试专家',
    available: true,
    color: 'bg-fuchsia-400',
    icon: '⚡'
  },
  {
    id: 'ping-tool-org',
    name: 'PingTool.org',
    description: 'Ping工具在线版',
    available: true,
    color: 'bg-violet-400',
    icon: '📡'
  },
  {
    id: 'network-test-cn',
    name: 'NetworkTest.cn',
    description: '网络测试中心',
    available: true,
    color: 'bg-purple-400',
    icon: '🌐'
  },
  {
    id: 'speed-tester',
    name: 'SpeedTester',
    description: '速度测试器',
    available: true,
    color: 'bg-indigo-400',
    icon: '🚀'
  },
  {
    id: 'ping-master',
    name: 'PingMaster',
    description: 'Ping测试大师',
    available: true,
    color: 'bg-blue-300',
    icon: '👑'
  },
  {
    id: 'net-monitor',
    name: 'NetMonitor',
    description: '网络监控工具',
    available: true,
    color: 'bg-cyan-300',
    icon: '📊'
  },
  {
    id: 'speed-check-cn',
    name: 'SpeedCheck.cn',
    description: '网速检查工具',
    available: true,
    color: 'bg-teal-300',
    icon: '✅'
  },
  {
    id: 'ping-test-pro',
    name: 'PingTest Pro',
    description: 'Ping测试专业版',
    available: true,
    color: 'bg-emerald-300',
    icon: '💎'
  },

  // 更多国外平台 - 第三波扩展
  {
    id: 'pingplotter',
    name: 'PingPlotter',
    description: '网络故障诊断工具',
    available: getPlatformAvailability('pingplotter', false),
    color: 'bg-slate-700',
    icon: '📈'
  },
  {
    id: 'paessler-prtg',
    name: 'PRTG Network Monitor',
    description: 'Paessler网络监控',
    available: getPlatformAvailability('prtg', false),
    color: 'bg-gray-700',
    icon: '🖥️'
  },
  {
    id: 'solarwinds',
    name: 'SolarWinds NPM',
    description: 'SolarWinds网络性能监控',
    available: getPlatformAvailability('solarwinds', false),
    color: 'bg-zinc-700',
    icon: '☀️'
  },
  {
    id: 'nagios',
    name: 'Nagios XI',
    description: 'Nagios网络监控',
    available: getPlatformAvailability('nagios', false),
    color: 'bg-neutral-700',
    icon: '👁️‍🗨️'
  },
  {
    id: 'zabbix',
    name: 'Zabbix',
    description: '开源网络监控',
    available: getPlatformAvailability('zabbix', false),
    color: 'bg-stone-700',
    icon: '📡'
  },
  {
    id: 'icinga',
    name: 'Icinga',
    description: '网络监控解决方案',
    available: getPlatformAvailability('icinga', false),
    color: 'bg-red-800',
    icon: '🔍'
  },
  {
    id: 'observium',
    name: 'Observium',
    description: '网络监控平台',
    available: getPlatformAvailability('observium', false),
    color: 'bg-orange-800',
    icon: '👀'
  },
  {
    id: 'librenms',
    name: 'LibreNMS',
    description: '开源网络监控系统',
    available: getPlatformAvailability('librenms', false),
    color: 'bg-yellow-800',
    icon: '📊'
  },
  {
    id: 'pandora-fms',
    name: 'Pandora FMS',
    description: '网络监控软件',
    available: getPlatformAvailability('pandora-fms', false),
    color: 'bg-green-800',
    icon: '🐼'
  },
  {
    id: 'cacti',
    name: 'Cacti',
    description: '网络图形化监控',
    available: getPlatformAvailability('cacti', false),
    color: 'bg-blue-800',
    icon: '🌵'
  },

  // 全球各地区平台
  {
    id: 'ping-asia',
    name: 'Ping.Asia',
    description: '亚洲ping测试服务',
    available: getPlatformAvailability('ping-asia', false),
    color: 'bg-indigo-800',
    icon: '🌏'
  },
  {
    id: 'ping-america',
    name: 'Ping.America',
    description: '美洲ping测试服务',
    available: getPlatformAvailability('ping-america', false),
    color: 'bg-purple-800',
    icon: '🌎'
  },
  {
    id: 'ping-africa',
    name: 'Ping.Africa',
    description: '非洲ping测试服务',
    available: getPlatformAvailability('ping-africa', false),
    color: 'bg-pink-800',
    icon: '🌍'
  },
  {
    id: 'oceania-ping',
    name: 'Oceania Ping',
    description: '大洋洲ping测试',
    available: getPlatformAvailability('oceania-ping', false),
    color: 'bg-rose-800',
    icon: '🏝️'
  },

  // 更多云服务商
  {
    id: 'digitalocean-speed',
    name: 'DigitalOcean Speed',
    description: 'DigitalOcean速度测试',
    available: true,
    color: 'bg-blue-500',
    icon: '🌊'
  },
  {
    id: 'linode-speed',
    name: 'Linode Speed',
    description: 'Linode网络测试',
    available: true,
    color: 'bg-green-500',
    icon: '🔗'
  },
  {
    id: 'vultr-speed',
    name: 'Vultr Speed',
    description: 'Vultr性能测试',
    available: true,
    color: 'bg-purple-500',
    icon: '⚡'
  },
  {
    id: 'hetzner-speed',
    name: 'Hetzner Speed',
    description: 'Hetzner网络测试',
    available: true,
    color: 'bg-red-500',
    icon: '🏢'
  },
  {
    id: 'ovh-speed',
    name: 'OVH Speed',
    description: 'OVH网络性能测试',
    available: true,
    color: 'bg-indigo-500',
    icon: '🇫🇷'
  },

  // 更多CDN平台
  {
    id: 'fastly-speed',
    name: 'Fastly Speed',
    description: 'Fastly CDN测试',
    available: getPlatformAvailability('fastly', false),
    color: 'bg-cyan-600',
    icon: '⚡'
  },
  {
    id: 'maxcdn',
    name: 'MaxCDN',
    description: 'MaxCDN性能测试',
    available: getPlatformAvailability('maxcdn', false),
    color: 'bg-teal-600',
    icon: '📦'
  },
  {
    id: 'bunnycdn',
    name: 'BunnyCDN',
    description: 'BunnyCDN速度测试',
    available: getPlatformAvailability('bunnycdn', false),
    color: 'bg-emerald-600',
    icon: '🐰'
  },
  {
    id: 'stackpath',
    name: 'StackPath',
    description: 'StackPath CDN测试',
    available: getPlatformAvailability('stackpath', false),
    color: 'bg-lime-600',
    icon: '📚'
  },

  // 移动网络测试
  {
    id: '4g-test',
    name: '4G Network Test',
    description: '4G网络速度测试',
    available: true,
    color: 'bg-yellow-400',
    icon: '📱'
  },
  {
    id: '5g-test',
    name: '5G Network Test',
    description: '5G网络速度测试',
    available: true,
    color: 'bg-amber-400',
    icon: '📶'
  },
  {
    id: 'wifi-analyzer',
    name: 'WiFi Analyzer',
    description: 'WiFi网络分析',
    available: true,
    color: 'bg-orange-300',
    icon: '📡'
  },

  // 游戏网络测试
  {
    id: 'steam-ping',
    name: 'Steam Ping',
    description: 'Steam服务器ping测试',
    available: true,
    color: 'bg-slate-600',
    icon: '🎮'
  },
  {
    id: 'xbox-ping',
    name: 'Xbox Live Ping',
    description: 'Xbox Live网络测试',
    available: true,
    color: 'bg-green-600',
    icon: '🎮'
  },
  {
    id: 'psn-ping',
    name: 'PSN Ping',
    description: 'PlayStation网络测试',
    available: true,
    color: 'bg-blue-600',
    icon: '🎮'
  },
  {
    id: 'nintendo-ping',
    name: 'Nintendo Ping',
    description: '任天堂网络测试',
    available: true,
    color: 'bg-red-600',
    icon: '🎮'
  },

  // 流媒体测试
  {
    id: 'youtube-ping',
    name: 'YouTube Ping',
    description: 'YouTube服务器测试',
    available: getPlatformAvailability('youtube-ping', false),
    color: 'bg-red-500',
    icon: '📺'
  },
  {
    id: 'twitch-ping',
    name: 'Twitch Ping',
    description: 'Twitch服务器测试',
    available: getPlatformAvailability('twitch-ping', false),
    color: 'bg-purple-600',
    icon: '📹'
  },
  {
    id: 'bilibili-ping',
    name: 'Bilibili Ping',
    description: 'B站服务器测试',
    available: true,
    color: 'bg-pink-500',
    icon: '📺'
  },

  // 社交媒体测试
  {
    id: 'discord-ping',
    name: 'Discord Ping',
    description: 'Discord服务器测试',
    available: false,
    color: 'bg-indigo-600',
    icon: '💬'
  },
  {
    id: 'telegram-ping',
    name: 'Telegram Ping',
    description: 'Telegram服务器测试',
    available: false,
    color: 'bg-blue-400',
    icon: '✈️'
  },
  {
    id: 'wechat-ping',
    name: 'WeChat Ping',
    description: '微信服务器测试',
    available: true,
    color: 'bg-green-400',
    icon: '💬'
  },
  {
    id: 'qq-ping',
    name: 'QQ Ping',
    description: 'QQ服务器测试',
    available: true,
    color: 'bg-blue-400',
    icon: '🐧'
  },

  // 电商平台测试
  {
    id: 'shopify-ping',
    name: 'Shopify Ping',
    description: 'Shopify服务器测试',
    available: false,
    color: 'bg-green-500',
    icon: '🛒'
  },
  {
    id: 'woocommerce-ping',
    name: 'WooCommerce Ping',
    description: 'WooCommerce测试',
    available: false,
    color: 'bg-purple-500',
    icon: '🛍️'
  },

  // 开发者平台测试
  {
    id: 'gitlab-ping',
    name: 'GitLab Ping',
    description: 'GitLab服务器测试',
    available: false,
    color: 'bg-orange-600',
    icon: '🦊'
  },
  {
    id: 'bitbucket-ping',
    name: 'Bitbucket Ping',
    description: 'Bitbucket服务器测试',
    available: false,
    color: 'bg-blue-700',
    icon: '🪣'
  },
  {
    id: 'gitee-ping',
    name: 'Gitee Ping',
    description: 'Gitee服务器测试',
    available: true,
    color: 'bg-red-400',
    icon: '🌿'
  },

  // 邮件服务测试
  {
    id: 'gmail-ping',
    name: 'Gmail Ping',
    description: 'Gmail服务器测试',
    available: false,
    color: 'bg-red-600',
    icon: '📧'
  },
  {
    id: 'outlook-ping',
    name: 'Outlook Ping',
    description: 'Outlook服务器测试',
    available: false,
    color: 'bg-blue-600',
    icon: '📨'
  },
  {
    id: '163mail-ping',
    name: '163邮箱 Ping',
    description: '网易邮箱服务器测试',
    available: true,
    color: 'bg-green-400',
    icon: '📮'
  },
  {
    id: 'qqmail-ping',
    name: 'QQ邮箱 Ping',
    description: 'QQ邮箱服务器测试',
    available: true,
    color: 'bg-blue-400',
    icon: '📬'
  },

  // 搜索引擎测试
  {
    id: 'duckduckgo-ping',
    name: 'DuckDuckGo Ping',
    description: 'DuckDuckGo搜索测试',
    available: false,
    color: 'bg-orange-500',
    icon: '🦆'
  },
  {
    id: 'yandex-ping',
    name: 'Yandex Ping',
    description: 'Yandex搜索测试',
    available: false,
    color: 'bg-red-500',
    icon: '🔍'
  },
  {
    id: 'so-com-ping',
    name: '360搜索 Ping',
    description: '360搜索服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '🔍'
  },

  // 新闻媒体测试
  {
    id: 'reuters-ping',
    name: 'Reuters Ping',
    description: '路透社服务器测试',
    available: false,
    color: 'bg-orange-700',
    icon: '📰'
  },
  {
    id: 'ap-ping',
    name: 'AP News Ping',
    description: '美联社新闻测试',
    available: false,
    color: 'bg-blue-700',
    icon: '📰'
  },
  {
    id: 'xinhua-ping',
    name: '新华网 Ping',
    description: '新华网服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '📰'
  },
  {
    id: 'people-ping',
    name: '人民网 Ping',
    description: '人民网服务器测试',
    available: true,
    color: 'bg-red-600',
    icon: '📰'
  },

  // 金融服务测试
  {
    id: 'paypal-ping',
    name: 'PayPal Ping',
    description: 'PayPal服务器测试',
    available: false,
    color: 'bg-blue-500',
    icon: '💳'
  },
  {
    id: 'stripe-ping',
    name: 'Stripe Ping',
    description: 'Stripe支付测试',
    available: false,
    color: 'bg-purple-600',
    icon: '💳'
  },
  {
    id: 'alipay-ping',
    name: '支付宝 Ping',
    description: '支付宝服务器测试',
    available: true,
    color: 'bg-blue-400',
    icon: '💰'
  },
  {
    id: 'wechatpay-ping',
    name: '微信支付 Ping',
    description: '微信支付服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '💰'
  },

  // 教育平台测试
  {
    id: 'coursera-ping',
    name: 'Coursera Ping',
    description: 'Coursera平台测试',
    available: false,
    color: 'bg-blue-600',
    icon: '🎓'
  },
  {
    id: 'edx-ping',
    name: 'edX Ping',
    description: 'edX平台测试',
    available: false,
    color: 'bg-purple-600',
    icon: '📚'
  },
  {
    id: 'xuetangx-ping',
    name: '学堂在线 Ping',
    description: '学堂在线平台测试',
    available: true,
    color: 'bg-green-500',
    icon: '🎓'
  },

  // 云存储测试
  {
    id: 'dropbox-ping',
    name: 'Dropbox Ping',
    description: 'Dropbox服务器测试',
    available: false,
    color: 'bg-blue-500',
    icon: '📦'
  },
  {
    id: 'onedrive-ping',
    name: 'OneDrive Ping',
    description: 'OneDrive服务器测试',
    available: false,
    color: 'bg-blue-600',
    icon: '☁️'
  },
  {
    id: 'baiduyun-ping',
    name: '百度网盘 Ping',
    description: '百度网盘服务器测试',
    available: true,
    color: 'bg-blue-400',
    icon: '☁️'
  },
  {
    id: 'aliyundrive-ping',
    name: '阿里云盘 Ping',
    description: '阿里云盘服务器测试',
    available: true,
    color: 'bg-orange-400',
    icon: '☁️'
  },

  // 终极扩展 - 第四波：银行金融平台
  {
    id: 'icbc-ping',
    name: '工商银行 Ping',
    description: '中国工商银行服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '🏦'
  },
  {
    id: 'ccb-ping',
    name: '建设银行 Ping',
    description: '中国建设银行服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '🏦'
  },
  {
    id: 'abc-ping',
    name: '农业银行 Ping',
    description: '中国农业银行服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '🏦'
  },
  {
    id: 'boc-ping',
    name: '中国银行 Ping',
    description: '中国银行服务器测试',
    available: true,
    color: 'bg-red-600',
    icon: '🏦'
  },
  {
    id: 'cmb-ping',
    name: '招商银行 Ping',
    description: '招商银行服务器测试',
    available: true,
    color: 'bg-red-400',
    icon: '🏦'
  },

  // 电商购物平台
  {
    id: 'taobao-ping',
    name: '淘宝 Ping',
    description: '淘宝服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '🛒'
  },
  {
    id: 'tmall-ping',
    name: '天猫 Ping',
    description: '天猫服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '🛒'
  },
  {
    id: 'jd-ping',
    name: '京东 Ping',
    description: '京东服务器测试',
    available: true,
    color: 'bg-red-600',
    icon: '🛒'
  },
  {
    id: 'pdd-ping',
    name: '拼多多 Ping',
    description: '拼多多服务器测试',
    available: true,
    color: 'bg-orange-400',
    icon: '🛒'
  },
  {
    id: 'amazon-ping',
    name: 'Amazon Ping',
    description: '亚马逊服务器测试',
    available: false,
    color: 'bg-yellow-600',
    icon: '🛒'
  },
  {
    id: 'ebay-ping',
    name: 'eBay Ping',
    description: 'eBay服务器测试',
    available: false,
    color: 'bg-blue-600',
    icon: '🛒'
  },

  // 出行交通平台
  {
    id: 'didi-ping',
    name: '滴滴 Ping',
    description: '滴滴出行服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '🚗'
  },
  {
    id: '12306-ping',
    name: '12306 Ping',
    description: '中国铁路12306测试',
    available: true,
    color: 'bg-blue-600',
    icon: '🚄'
  },
  {
    id: 'ctrip-ping',
    name: '携程 Ping',
    description: '携程旅行服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '✈️'
  },
  {
    id: 'qunar-ping',
    name: '去哪儿 Ping',
    description: '去哪儿网服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '✈️'
  },
  {
    id: 'uber-ping',
    name: 'Uber Ping',
    description: 'Uber服务器测试',
    available: false,
    color: 'bg-black',
    icon: '🚗'
  },

  // 外卖配送平台
  {
    id: 'meituan-ping',
    name: '美团 Ping',
    description: '美团服务器测试',
    available: true,
    color: 'bg-yellow-500',
    icon: '🍔'
  },
  {
    id: 'eleme-ping',
    name: '饿了么 Ping',
    description: '饿了么服务器测试',
    available: true,
    color: 'bg-blue-400',
    icon: '🍔'
  },

  // 地图导航平台
  {
    id: 'baidu-map-ping',
    name: '百度地图 Ping',
    description: '百度地图服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '🗺️'
  },
  {
    id: 'amap-ping',
    name: '高德地图 Ping',
    description: '高德地图服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '🗺️'
  },
  {
    id: 'tencent-map-ping',
    name: '腾讯地图 Ping',
    description: '腾讯地图服务器测试',
    available: true,
    color: 'bg-green-600',
    icon: '🗺️'
  },
  {
    id: 'google-maps-ping',
    name: 'Google Maps Ping',
    description: '谷歌地图服务器测试',
    available: false,
    color: 'bg-green-600',
    icon: '🗺️'
  },

  // 音乐平台
  {
    id: 'netease-music-ping',
    name: '网易云音乐 Ping',
    description: '网易云音乐服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '🎵'
  },
  {
    id: 'qq-music-ping',
    name: 'QQ音乐 Ping',
    description: 'QQ音乐服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '🎵'
  },
  {
    id: 'kugou-ping',
    name: '酷狗音乐 Ping',
    description: '酷狗音乐服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '🎵'
  },
  {
    id: 'kuwo-ping',
    name: '酷我音乐 Ping',
    description: '酷我音乐服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '🎵'
  },
  {
    id: 'spotify-ping',
    name: 'Spotify Ping',
    description: 'Spotify服务器测试',
    available: false,
    color: 'bg-green-600',
    icon: '🎵'
  },
  {
    id: 'apple-music-ping',
    name: 'Apple Music Ping',
    description: 'Apple Music服务器测试',
    available: false,
    color: 'bg-gray-800',
    icon: '🎵'
  },

  // 视频平台扩展
  {
    id: 'iqiyi-ping',
    name: '爱奇艺 Ping',
    description: '爱奇艺服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '📺'
  },
  {
    id: 'youku-ping',
    name: '优酷 Ping',
    description: '优酷服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '📺'
  },
  {
    id: 'tencent-video-ping',
    name: '腾讯视频 Ping',
    description: '腾讯视频服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '📺'
  },
  {
    id: 'mango-tv-ping',
    name: '芒果TV Ping',
    description: '芒果TV服务器测试',
    available: true,
    color: 'bg-yellow-500',
    icon: '📺'
  },
  {
    id: 'netflix-ping',
    name: 'Netflix Ping',
    description: 'Netflix服务器测试',
    available: false,
    color: 'bg-red-600',
    icon: '📺'
  },
  {
    id: 'hulu-ping',
    name: 'Hulu Ping',
    description: 'Hulu服务器测试',
    available: false,
    color: 'bg-green-600',
    icon: '📺'
  },

  // 新闻资讯平台
  {
    id: 'toutiao-ping',
    name: '今日头条 Ping',
    description: '今日头条服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '📰'
  },
  {
    id: 'sina-ping',
    name: '新浪 Ping',
    description: '新浪网服务器测试',
    available: true,
    color: 'bg-red-600',
    icon: '📰'
  },
  {
    id: 'sohu-ping',
    name: '搜狐 Ping',
    description: '搜狐网服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '📰'
  },
  {
    id: 'netease-ping',
    name: '网易 Ping',
    description: '网易网服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '📰'
  },

  // 办公协作平台
  {
    id: 'dingtalk-ping',
    name: '钉钉 Ping',
    description: '钉钉服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '💼'
  },
  {
    id: 'feishu-ping',
    name: '飞书 Ping',
    description: '飞书服务器测试',
    available: true,
    color: 'bg-blue-600',
    icon: '💼'
  },
  {
    id: 'wework-ping',
    name: '企业微信 Ping',
    description: '企业微信服务器测试',
    available: true,
    color: 'bg-green-600',
    icon: '💼'
  },
  {
    id: 'slack-ping',
    name: 'Slack Ping',
    description: 'Slack服务器测试',
    available: false,
    color: 'bg-purple-600',
    icon: '💼'
  },
  {
    id: 'teams-ping',
    name: 'Microsoft Teams Ping',
    description: 'Microsoft Teams测试',
    available: false,
    color: 'bg-blue-600',
    icon: '💼'
  },
  {
    id: 'zoom-ping',
    name: 'Zoom Ping',
    description: 'Zoom服务器测试',
    available: false,
    color: 'bg-blue-500',
    icon: '💼'
  },

  // 学习教育平台扩展
  {
    id: 'zhihu-ping',
    name: '知乎 Ping',
    description: '知乎服务器测试',
    available: true,
    color: 'bg-blue-600',
    icon: '🎓'
  },
  {
    id: 'csdn-ping',
    name: 'CSDN Ping',
    description: 'CSDN服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '🎓'
  },
  {
    id: 'jianshu-ping',
    name: '简书 Ping',
    description: '简书服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '🎓'
  },
  {
    id: 'segmentfault-ping',
    name: 'SegmentFault Ping',
    description: 'SegmentFault测试',
    available: true,
    color: 'bg-green-600',
    icon: '🎓'
  },

  // 更多国外社交平台
  {
    id: 'facebook-ping',
    name: 'Facebook Ping',
    description: 'Facebook服务器测试',
    available: getPlatformAvailability('facebook-ping', false),
    color: 'bg-blue-600',
    icon: '📘'
  },
  {
    id: 'twitter-ping',
    name: 'Twitter Ping',
    description: 'Twitter服务器测试',
    available: getPlatformAvailability('twitter-ping', false),
    color: 'bg-blue-400',
    icon: '🐦'
  },
  {
    id: 'instagram-ping',
    name: 'Instagram Ping',
    description: 'Instagram服务器测试',
    available: getPlatformAvailability('instagram-ping', false),
    color: 'bg-pink-500',
    icon: '📷'
  },
  {
    id: 'linkedin-ping',
    name: 'LinkedIn Ping',
    description: 'LinkedIn服务器测试',
    available: getPlatformAvailability('linkedin-ping', false),
    color: 'bg-blue-700',
    icon: '💼'
  },
  {
    id: 'tiktok-ping',
    name: 'TikTok Ping',
    description: 'TikTok服务器测试',
    available: getPlatformAvailability('tiktok-ping', false),
    color: 'bg-black',
    icon: '🎵'
  },
  {
    id: 'douyin-ping',
    name: '抖音 Ping',
    description: '抖音服务器测试',
    available: true,
    color: 'bg-black',
    icon: '🎵'
  },

  // 更多云服务和托管平台
  {
    id: 'github-pages-ping',
    name: 'GitHub Pages Ping',
    description: 'GitHub Pages测试',
    available: false,
    color: 'bg-gray-800',
    icon: '📄'
  },
  {
    id: 'netlify-ping',
    name: 'Netlify Ping',
    description: 'Netlify服务器测试',
    available: false,
    color: 'bg-teal-500',
    icon: '🌐'
  },
  {
    id: 'heroku-ping',
    name: 'Heroku Ping',
    description: 'Heroku服务器测试',
    available: false,
    color: 'bg-purple-600',
    icon: '🚀'
  },
  {
    id: 'railway-ping',
    name: 'Railway Ping',
    description: 'Railway服务器测试',
    available: false,
    color: 'bg-black',
    icon: '🚂'
  },
  {
    id: 'render-ping',
    name: 'Render Ping',
    description: 'Render服务器测试',
    available: false,
    color: 'bg-green-500',
    icon: '🎨'
  },

  // 更多CDN和边缘计算
  {
    id: 'amazon-cloudfront-ping',
    name: 'Amazon CloudFront',
    description: 'AWS CloudFront CDN测试',
    available: false,
    color: 'bg-orange-600',
    icon: '☁️'
  },
  {
    id: 'azure-cdn-ping',
    name: 'Azure CDN',
    description: 'Microsoft Azure CDN测试',
    available: false,
    color: 'bg-blue-600',
    icon: '☁️'
  },
  {
    id: 'google-cloud-cdn-ping',
    name: 'Google Cloud CDN',
    description: 'Google Cloud CDN测试',
    available: false,
    color: 'bg-blue-500',
    icon: '☁️'
  },
  {
    id: 'keycdn-ping',
    name: 'KeyCDN',
    description: 'KeyCDN服务器测试',
    available: false,
    color: 'bg-blue-400',
    icon: '🔑'
  },

  // 区块链和加密货币平台
  {
    id: 'binance-ping',
    name: 'Binance Ping',
    description: '币安交易所测试',
    available: false,
    color: 'bg-yellow-500',
    icon: '₿'
  },
  {
    id: 'coinbase-ping',
    name: 'Coinbase Ping',
    description: 'Coinbase交易所测试',
    available: false,
    color: 'bg-blue-600',
    icon: '₿'
  },
  {
    id: 'huobi-ping',
    name: '火币 Ping',
    description: '火币交易所测试',
    available: true,
    color: 'bg-blue-500',
    icon: '₿'
  },
  {
    id: 'okx-ping',
    name: 'OKX Ping',
    description: 'OKX交易所测试',
    available: true,
    color: 'bg-blue-600',
    icon: '₿'
  },

  // 更多游戏平台
  {
    id: 'epic-games-ping',
    name: 'Epic Games Ping',
    description: 'Epic Games服务器测试',
    available: false,
    color: 'bg-gray-800',
    icon: '🎮'
  },
  {
    id: 'origin-ping',
    name: 'Origin Ping',
    description: 'EA Origin服务器测试',
    available: false,
    color: 'bg-orange-600',
    icon: '🎮'
  },
  {
    id: 'uplay-ping',
    name: 'Uplay Ping',
    description: 'Ubisoft Uplay测试',
    available: false,
    color: 'bg-blue-600',
    icon: '🎮'
  },
  {
    id: 'battle-net-ping',
    name: 'Battle.net Ping',
    description: '暴雪战网服务器测试',
    available: false,
    color: 'bg-blue-800',
    icon: '🎮'
  },
  {
    id: 'wegame-ping',
    name: 'WeGame Ping',
    description: '腾讯WeGame测试',
    available: true,
    color: 'bg-blue-500',
    icon: '🎮'
  },

  // 更多开发者工具和平台
  {
    id: 'stackoverflow-ping',
    name: 'Stack Overflow Ping',
    description: 'Stack Overflow测试',
    available: false,
    color: 'bg-orange-600',
    icon: '💻'
  },
  {
    id: 'codepen-ping',
    name: 'CodePen Ping',
    description: 'CodePen服务器测试',
    available: false,
    color: 'bg-black',
    icon: '💻'
  },
  {
    id: 'jsfiddle-ping',
    name: 'JSFiddle Ping',
    description: 'JSFiddle服务器测试',
    available: false,
    color: 'bg-blue-600',
    icon: '💻'
  },
  {
    id: 'replit-ping',
    name: 'Replit Ping',
    description: 'Replit服务器测试',
    available: false,
    color: 'bg-orange-500',
    icon: '💻'
  },
  {
    id: 'codesandbox-ping',
    name: 'CodeSandbox Ping',
    description: 'CodeSandbox测试',
    available: false,
    color: 'bg-black',
    icon: '💻'
  },

  // 更多企业服务平台
  {
    id: 'salesforce-ping',
    name: 'Salesforce Ping',
    description: 'Salesforce服务器测试',
    available: false,
    color: 'bg-blue-500',
    icon: '🏢'
  },
  {
    id: 'oracle-ping',
    name: 'Oracle Ping',
    description: 'Oracle服务器测试',
    available: false,
    color: 'bg-red-600',
    icon: '🏢'
  },
  {
    id: 'sap-ping',
    name: 'SAP Ping',
    description: 'SAP服务器测试',
    available: false,
    color: 'bg-blue-600',
    icon: '🏢'
  },
  {
    id: 'ibm-ping',
    name: 'IBM Ping',
    description: 'IBM服务器测试',
    available: false,
    color: 'bg-blue-800',
    icon: '🏢'
  },

  // 更多国内互联网平台
  {
    id: 'xiaomi-ping',
    name: '小米 Ping',
    description: '小米服务器测试',
    available: true,
    color: 'bg-orange-500',
    icon: '📱'
  },
  {
    id: 'huawei-ping',
    name: '华为 Ping',
    description: '华为服务器测试',
    available: true,
    color: 'bg-red-600',
    icon: '📱'
  },
  {
    id: 'oppo-ping',
    name: 'OPPO Ping',
    description: 'OPPO服务器测试',
    available: true,
    color: 'bg-green-500',
    icon: '📱'
  },
  {
    id: 'vivo-ping',
    name: 'vivo Ping',
    description: 'vivo服务器测试',
    available: true,
    color: 'bg-blue-500',
    icon: '📱'
  },
  {
    id: 'oneplus-ping',
    name: '一加 Ping',
    description: '一加服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '📱'
  },

  // 更多国外科技公司
  {
    id: 'apple-ping',
    name: 'Apple Ping',
    description: '苹果服务器测试',
    available: getPlatformAvailability('apple-ping', false),
    color: 'bg-gray-800',
    icon: '🍎'
  },
  {
    id: 'microsoft-ping',
    name: 'Microsoft Ping',
    description: '微软服务器测试',
    available: getPlatformAvailability('microsoft-ping', false),
    color: 'bg-blue-600',
    icon: '🪟'
  },
  {
    id: 'google-ping',
    name: 'Google Ping',
    description: '谷歌服务器测试',
    available: getPlatformAvailability('google-ping', false),
    color: 'bg-blue-500',
    icon: '🔍'
  },
  {
    id: 'meta-ping',
    name: 'Meta Ping',
    description: 'Meta(Facebook)测试',
    available: false,
    color: 'bg-blue-600',
    icon: '🌐'
  },
  {
    id: 'tesla-ping',
    name: 'Tesla Ping',
    description: '特斯拉服务器测试',
    available: false,
    color: 'bg-red-600',
    icon: '🚗'
  },

  // 更多国际电商平台
  {
    id: 'aliexpress-ping',
    name: 'AliExpress Ping',
    description: '全球速卖通测试',
    available: true,
    color: 'bg-orange-500',
    icon: '🛒'
  },
  {
    id: 'wish-ping',
    name: 'Wish Ping',
    description: 'Wish服务器测试',
    available: false,
    color: 'bg-blue-500',
    icon: '🛒'
  },
  {
    id: 'etsy-ping',
    name: 'Etsy Ping',
    description: 'Etsy服务器测试',
    available: false,
    color: 'bg-orange-600',
    icon: '🛒'
  },

  // 更多在线服务平台
  {
    id: 'canva-ping',
    name: 'Canva Ping',
    description: 'Canva设计平台测试',
    available: false,
    color: 'bg-purple-500',
    icon: '🎨'
  },
  {
    id: 'figma-ping',
    name: 'Figma Ping',
    description: 'Figma设计工具测试',
    available: false,
    color: 'bg-purple-600',
    icon: '🎨'
  },
  {
    id: 'notion-ping',
    name: 'Notion Ping',
    description: 'Notion服务器测试',
    available: false,
    color: 'bg-gray-800',
    icon: '📝'
  },
  {
    id: 'airtable-ping',
    name: 'Airtable Ping',
    description: 'Airtable服务器测试',
    available: false,
    color: 'bg-yellow-500',
    icon: '📊'
  },

  // 更多国内服务平台
  {
    id: 'douban-ping',
    name: '豆瓣 Ping',
    description: '豆瓣服务器测试',
    available: true,
    color: 'bg-green-600',
    icon: '📚'
  },
  {
    id: 'tieba-ping',
    name: '百度贴吧 Ping',
    description: '百度贴吧服务器测试',
    available: true,
    color: 'bg-blue-600',
    icon: '💬'
  },
  {
    id: 'weibo-ping',
    name: '微博 Ping',
    description: '新浪微博服务器测试',
    available: true,
    color: 'bg-red-500',
    icon: '📱'
  },
  {
    id: 'xiaohongshu-ping',
    name: '小红书 Ping',
    description: '小红书服务器测试',
    available: true,
    color: 'bg-red-400',
    icon: '📱'
  }
];

// 使用配置文件中的测试网站列表
const TEST_SITES = ALL_TEST_SITES;

export default function MultiPingPage() {
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [selectedSite, setSelectedSite] = useState<TestSite>(getRecommendedSites()[0]);
  const [customUrl, setCustomUrl] = useState('');
  const [useCustomUrl, setUseCustomUrl] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, PingResult>>({});
  const [isTestingAll, setIsTestingAll] = useState(false);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(
    ['fast-com', 'aws-ping', 'huawei-cloud'] // 优化组合：Fast.com + AWS CloudPing + 华为云测速
  );
  const [platformFilter, setPlatformFilter] = useState<'all' | 'available' | 'domestic' | 'foreign' | 'cloud' | 'isp' | 'cdn' | 'tools' | 'gaming' | 'social' | 'streaming' | 'enterprise' | 'ecommerce' | 'finance' | 'music' | 'lifestyle'>('available');
  const [siteFilter, setSiteFilter] = useState<'all' | 'recommended' | 'domestic' | 'foreign' | 'regional'>('recommended');
  const [cityTierFilter, setCityTierFilter] = useState<'all' | 1 | 2 | 3 | 4>('all');
  const [userCityTier, setUserCityTier] = useState<1 | 2 | 3 | 4>(1); // 用户所在城市等级，用于计算预期延迟
  const [subcategoryFilter, setSubcategoryFilter] = useState<string>('all');
  const [batchMode, setBatchMode] = useState(false);
  const [selectedSites, setSelectedSites] = useState<TestSite[]>([]);
  const [batchResults, setBatchResults] = useState<Record<string, Record<string, PingResult>>>({});
  const [isBatchTesting, setIsBatchTesting] = useState(false);
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie'>('bar');

  // 计算预期延迟
  const calculateExpectedLatency = (site: TestSite, userTier: 1 | 2 | 3 | 4): number => {
    if (!site.latencyMultiplier) {
      return site.expectedLatency.domestic;
    }

    // 基础延迟 (假设一线城市到一线城市的基础延迟为30ms)
    const baseLatency = 30;

    // 根据用户城市等级和目标城市等级计算延迟倍数
    const multiplierKey = `tier${userTier}` as keyof typeof site.latencyMultiplier;
    const multiplier = site.latencyMultiplier[multiplierKey];

    return Math.round(baseLatency * multiplier);
  };

  // 获取延迟等级描述
  const getLatencyDescription = (latency: number): { text: string; color: string } => {
    if (latency <= 30) return { text: '极快', color: 'text-green-600' };
    if (latency <= 50) return { text: '较快', color: 'text-blue-600' };
    if (latency <= 80) return { text: '一般', color: 'text-yellow-600' };
    if (latency <= 120) return { text: '较慢', color: 'text-orange-600' };
    return { text: '很慢', color: 'text-red-600' };
  };

  // 获取过滤后的网站列表
  const getFilteredSites = (): TestSite[] => {
    let sites: TestSite[] = [];

    switch (siteFilter) {
      case 'recommended':
        sites = getRecommendedSites();
        break;
      case 'domestic':
        sites = getSitesByCategory('domestic');
        break;
      case 'foreign':
        sites = getSitesByCategory('foreign');
        break;
      case 'regional':
        sites = getSitesByCategory('regional');
        break;
      default:
        sites = ALL_TEST_SITES;
    }

    // 按城市等级过滤
    if (cityTierFilter !== 'all') {
      sites = sites.filter(site => site.cityTier === cityTierFilter);
    }

    if (subcategoryFilter !== 'all') {
      sites = sites.filter(site => site.subcategory === subcategoryFilter);
    }

    return sites;
  };

  const filteredSites = getFilteredSites();

  // 获取过滤后的平台列表
  const getFilteredPlatforms = (): PingPlatform[] => {
    switch (platformFilter) {
      case 'available':
        return PING_PLATFORMS.filter(p => p.available);
      case 'domestic':
        return PING_PLATFORMS.filter(p =>
          ['itdog', '17ce', 'boce', 'chinaz', 'webkaka', 'ce8', 'ipip', 'ping-pe', 'alicloud', 'tencent-cloud', 'huawei-cloud',
           'tool-lu', 'ip138', 'ip-cn', 'linkwan', 'netsh', 'ping-chinaz', 'webluker', 'mmtrix', 'netspeedtestmaster', 'speedcn',
           'bootcdn', 'staticfile', 'baidu-cdn', 'mtr-sh', 'traceroute-online', 'nslookup-io', 'whois-net', 'dig-web'].includes(p.id)
        );
      case 'foreign':
        return PING_PLATFORMS.filter(p =>
          ['globalping', 'keycdn', 'pingdom', 'gtmetrix', 'uptrends', 'dotcom-tools', 'site24x7', 'host-tracker',
           'whatismyip', 'network-tools', 'ping-eu', 'just-ping', 'ca-app-synthetic', 'monitis', 'alertsite',
           'keynote', 'gomez', 'neustar', 'cedexis', 'thousandeyes', 'catchpoint', 'broadband-now', 'speedof-me', 'testmy-net'].includes(p.id)
        );
      case 'cloud':
        return PING_PLATFORMS.filter(p =>
          ['cloudflare', 'aws-ping', 'azure-ping', 'gcp-ping', 'alicloud', 'tencent-cloud', 'huawei-cloud',
           'speedtest', 'fast-com', 'librespeed', 'jsdelivr', 'unpkg', 'cdnjs', 'bootcdn', 'staticfile', 'baidu-cdn'].includes(p.id)
        );
      case 'isp':
        return PING_PLATFORMS.filter(p =>
          ['china-telecom', 'china-unicom', 'china-mobile', 'broadband-now', 'speedof-me', 'testmy-net', 'speedtest', 'fast-com'].includes(p.id)
        );
      case 'cdn':
        return PING_PLATFORMS.filter(p =>
          ['cloudflare', 'jsdelivr', 'unpkg', 'cdnjs', 'bootcdn', 'staticfile', 'baidu-cdn', 'keycdn'].includes(p.id)
        );
      case 'tools':
        return PING_PLATFORMS.filter(p =>
          ['mtr-sh', 'traceroute-online', 'nslookup-io', 'whois-net', 'dig-web', 'tool-lu', 'ip138', 'ip-cn', 'linkwan', 'netsh',
           'ip-tool', 'ping-tool-org', 'network-test-cn', 'ping-master', 'net-monitor'].includes(p.id)
        );
      case 'gaming':
        return PING_PLATFORMS.filter(p =>
          ['steam-ping', 'xbox-ping', 'psn-ping', 'nintendo-ping'].includes(p.id)
        );
      case 'social':
        return PING_PLATFORMS.filter(p =>
          ['discord-ping', 'telegram-ping', 'wechat-ping', 'qq-ping', 'youtube-ping', 'twitch-ping', 'bilibili-ping'].includes(p.id)
        );
      case 'streaming':
        return PING_PLATFORMS.filter(p =>
          ['youtube-ping', 'twitch-ping', 'bilibili-ping', 'fast-com'].includes(p.id)
        );
      case 'enterprise':
        return PING_PLATFORMS.filter(p =>
          ['pingplotter', 'paessler-prtg', 'solarwinds', 'nagios', 'zabbix', 'icinga', 'observium', 'librenms', 'pandora-fms', 'cacti',
           'thousandeyes', 'catchpoint', 'monitis', 'alertsite', 'keynote', 'gomez', 'neustar', 'cedexis', 'salesforce-ping',
           'oracle-ping', 'sap-ping', 'ibm-ping', 'dingtalk-ping', 'feishu-ping', 'wework-ping', 'slack-ping', 'teams-ping', 'zoom-ping'].includes(p.id)
        );
      case 'ecommerce':
        return PING_PLATFORMS.filter(p =>
          ['taobao-ping', 'tmall-ping', 'jd-ping', 'pdd-ping', 'amazon-ping', 'ebay-ping', 'shopify-ping', 'woocommerce-ping',
           'aliexpress-ping', 'wish-ping', 'etsy-ping'].includes(p.id)
        );
      case 'finance':
        return PING_PLATFORMS.filter(p =>
          ['icbc-ping', 'ccb-ping', 'abc-ping', 'boc-ping', 'cmb-ping', 'alipay-ping', 'wechatpay-ping', 'paypal-ping', 'stripe-ping',
           'binance-ping', 'coinbase-ping', 'huobi-ping', 'okx-ping'].includes(p.id)
        );
      case 'music':
        return PING_PLATFORMS.filter(p =>
          ['netease-music-ping', 'qq-music-ping', 'kugou-ping', 'kuwo-ping', 'spotify-ping', 'apple-music-ping'].includes(p.id)
        );
      case 'lifestyle':
        return PING_PLATFORMS.filter(p =>
          ['didi-ping', '12306-ping', 'ctrip-ping', 'qunar-ping', 'uber-ping', 'meituan-ping', 'eleme-ping', 'baidu-map-ping',
           'amap-ping', 'tencent-map-ping', 'google-maps-ping', 'douban-ping', 'tieba-ping', 'weibo-ping', 'xiaohongshu-ping',
           'toutiao-ping', 'sina-ping', 'sohu-ping', 'netease-ping', 'zhihu-ping'].includes(p.id)
        );
      default:
        return PING_PLATFORMS;
    }
  };

  const filteredPlatforms = getFilteredPlatforms();

  // 平台全选/取消全选
  const toggleSelectAllPlatforms = () => {
    const availablePlatformIds = filteredPlatforms.filter(p => p.available).map(p => p.id);
    if (selectedPlatforms.length === availablePlatformIds.length) {
      setSelectedPlatforms([]);
    } else {
      setSelectedPlatforms(availablePlatformIds);
    }
  };

  // 批量测试功能
  const runBatchTests = async () => {
    if (selectedSites.length === 0) {
      alert('请至少选择一个网站进行批量测试');
      return;
    }

    if (selectedPlatforms.length === 0) {
      alert('请至少选择一个测试平台');
      return;
    }

    setIsBatchTesting(true);
    setBatchResults({});

    try {
      // 使用批量API
      const response = await fetch('/api/multi-ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          urls: selectedSites.map(site => site.url),
          platforms: selectedPlatforms
        })
      });

      const data = await response.json();

      if (data.success) {
        // 转换批量结果格式
        const formattedResults: Record<string, Record<string, PingResult>> = {};

        data.batchResults.forEach((urlResult: any) => {
          const siteId = selectedSites.find(site => site.url === urlResult.url)?.id || urlResult.url;
          formattedResults[siteId] = {};

          urlResult.results.forEach((result: PingResult) => {
            formattedResults[siteId][result.platform] = result;
          });
        });

        setBatchResults(formattedResults);
      } else {
        alert(`批量测试失败: ${data.error}`);
      }
    } catch (error) {
      alert(`批量测试出错: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsBatchTesting(false);
    }
  };

  // 切换网站选择
  const toggleSiteSelection = (site: TestSite) => {
    setSelectedSites(prev => {
      const isSelected = prev.some(s => s.id === site.id);
      if (isSelected) {
        return prev.filter(s => s.id !== site.id);
      } else {
        return [...prev, site];
      }
    });
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedSites.length === filteredSites.length) {
      setSelectedSites([]);
    } else {
      setSelectedSites(filteredSites);
    }
  };

  // 执行单个平台的ping测试
  const testSinglePlatform = async (platform: PingPlatform, targetUrl: string): Promise<PingResult> => {
    const result: PingResult = {
      platform: platform.id,
      latency: null,
      status: 'pending',
      timestamp: new Date().toISOString()
    };

    try {
      // 使用统一的多平台API
      const response = await fetch(`/api/multi-ping?url=${encodeURIComponent(targetUrl)}&platforms=${platform.id}`);
      const data = await response.json();

      if (data.success && data.results && data.results.length > 0) {
        const platformResult = data.results[0];
        result.latency = platformResult.latency;
        result.status = platformResult.status;
        result.error = platformResult.error;
        result.timestamp = platformResult.timestamp;
      } else {
        result.status = 'error';
        result.error = data.error || '测试失败';
      }
    } catch (error) {
      result.status = 'error';
      result.error = error instanceof Error ? error.message : '网络错误';
    }

    return result;
  };

  // 执行所有选中平台的测试
  const runAllTests = async () => {
    const targetUrl = useCustomUrl ? customUrl : selectedSite.url;
    
    if (!targetUrl) {
      alert('请输入要测试的URL');
      return;
    }

    setIsTestingAll(true);
    setTestResults({});

    const availablePlatforms = PING_PLATFORMS.filter(p => 
      selectedPlatforms.includes(p.id) && p.available
    );

    // 初始化所有结果为pending状态
    const initialResults: Record<string, PingResult> = {};
    availablePlatforms.forEach(platform => {
      initialResults[platform.id] = {
        platform: platform.id,
        latency: null,
        status: 'pending'
      };
    });
    setTestResults(initialResults);

    // 并发执行所有测试
    const testPromises = availablePlatforms.map(async (platform) => {
      const result = await testSinglePlatform(platform, targetUrl);
      setTestResults(prev => ({
        ...prev,
        [platform.id]: result
      }));
      return result;
    });

    await Promise.all(testPromises);
    setIsTestingAll(false);
  };

  // 获取延迟状态颜色
  const getLatencyColor = (latency: number | null, status: string) => {
    if (status === 'pending') return 'text-yellow-500';
    if (status === 'error') return 'text-red-500';
    if (latency === null) return 'text-gray-500';
    
    if (latency < 50) return 'text-green-500';
    if (latency < 100) return 'text-yellow-500';
    if (latency < 200) return 'text-orange-500';
    return 'text-red-500';
  };

  // 格式化延迟显示
  const formatLatency = (result: PingResult) => {
    if (result.status === 'pending') return '测试中...';
    if (result.status === 'error') return `错误: ${result.error}`;
    if (result.latency === null) return '无数据';
    return `${result.latency}ms`;
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      {/* Header */}
      <div className="sticky top-0 z-50 backdrop-blur-sm bg-opacity-90 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className={`px-4 py-2 rounded-lg transition-colors duration-300 ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-white hover:bg-gray-100'} border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}
              >
                ← 返回首页
              </Link>
            </div>

            <div className="mr-4">
              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className={`p-3 rounded-lg shadow-lg transition-colors duration-300 ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-white hover:bg-gray-100'} border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}
                title={isDarkMode ? '切换到白天模式' : '切换到夜间模式'}
              >
                {isDarkMode ? '☀️' : '🌙'}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 测试配置区域 */}
        <div className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className="text-xl font-semibold mb-4">🎯 测试配置</h2>
          
          {/* 测试模式选择 */}
          <div className="mb-6">
            <div className="flex items-center space-x-6 mb-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={!batchMode}
                  onChange={() => {
                    setBatchMode(false);
                    setSelectedSites([]);
                    setBatchResults({});
                  }}
                  className="mr-2"
                />
                单个测试
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={batchMode}
                  onChange={() => {
                    setBatchMode(true);
                    setTestResults({});
                  }}
                  className="mr-2"
                />
                批量测试
              </label>
            </div>

            {!batchMode && (
              <div className="flex items-center space-x-4 mb-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={!useCustomUrl}
                    onChange={() => setUseCustomUrl(false)}
                    className="mr-2"
                  />
                  预设网站
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    checked={useCustomUrl}
                    onChange={() => setUseCustomUrl(true)}
                    className="mr-2"
                  />
                  自定义URL
                </label>
              </div>
            )}
          </div>

          {/* URL选择 */}
          <div className="mb-6">

            {!batchMode && !useCustomUrl ? (
              <div>
                {/* 网站过滤器 */}
                <div className="mb-6 space-y-4">
                  <div className="max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
                    <div className="flex flex-wrap gap-2 p-1">
                    <button
                      onClick={() => setSiteFilter('recommended')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'recommended'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🌟 推荐网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('all')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'all'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🌐 全部网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('domestic')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'domestic'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🇨🇳 国内网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('foreign')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'foreign'
                          ? 'bg-purple-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🌍 国外网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('regional')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'regional'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🏙️ 区域网站
                    </button>
                    </div>
                  </div>

                  {/* 城市等级过滤器 */}
                  {siteFilter === 'regional' && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      <button
                        onClick={() => setCityTierFilter('all')}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          cityTierFilter === 'all'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        全部城市
                      </button>
                      <button
                        onClick={() => setCityTierFilter(1)}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          cityTierFilter === 1
                            ? 'bg-red-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        一线城市
                      </button>
                      <button
                        onClick={() => setCityTierFilter(2)}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          cityTierFilter === 2
                            ? 'bg-orange-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        二线城市
                      </button>
                      <button
                        onClick={() => setCityTierFilter(3)}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          cityTierFilter === 3
                            ? 'bg-yellow-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        三线城市
                      </button>
                      <button
                        onClick={() => setCityTierFilter(4)}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          cityTierFilter === 4
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                        }`}
                      >
                        四线城市
                      </button>
                    </div>
                  )}

                  {/* 用户城市等级设置 */}
                  {siteFilter === 'regional' && (
                    <div className="mt-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                      <div className="flex flex-wrap gap-2 items-center mb-2">
                        <span className="text-sm font-medium text-blue-700 dark:text-blue-300">📍 您所在城市等级:</span>
                        <button
                          onClick={() => setUserCityTier(1)}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${
                            userCityTier === 1
                              ? 'bg-red-500 text-white'
                              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                          }`}
                        >
                          一线
                        </button>
                        <button
                          onClick={() => setUserCityTier(2)}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${
                            userCityTier === 2
                              ? 'bg-orange-500 text-white'
                              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                          }`}
                        >
                          二线
                        </button>
                        <button
                          onClick={() => setUserCityTier(3)}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${
                            userCityTier === 3
                              ? 'bg-yellow-500 text-white'
                              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                          }`}
                        >
                          三线
                        </button>
                        <button
                          onClick={() => setUserCityTier(4)}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${
                            userCityTier === 4
                              ? 'bg-green-500 text-white'
                              : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                          }`}
                        >
                          四线
                        </button>
                      </div>
                      <div className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                        <div>💡 设置后将显示从您的城市到目标城市的预期延迟倍数</div>
                        <div className="grid grid-cols-2 gap-2 mt-2">
                          <div className="text-xs">
                            <span className="font-medium">一线城市:</span> 北京、上海、广州、深圳
                          </div>
                          <div className="text-xs">
                            <span className="font-medium">二线城市:</span> 杭州、南京、武汉、成都、西安
                          </div>
                          <div className="text-xs">
                            <span className="font-medium">三线城市:</span> 合肥、昆明、南宁等省会城市
                          </div>
                          <div className="text-xs">
                            <span className="font-medium">四线城市:</span> 银川、拉萨等偏远地区
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 子分类过滤器 */}
                  <div className="max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
                    <div className="flex flex-wrap gap-2 p-1">
                    <button
                      onClick={() => setSubcategoryFilter('all')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        subcategoryFilter === 'all'
                          ? 'bg-gray-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
                      }`}
                    >
                      全部分类
                    </button>
                    {getAllSubcategories().map(subcategory => (
                      <button
                        key={subcategory}
                        onClick={() => setSubcategoryFilter(subcategory)}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          subcategoryFilter === subcategory
                            ? 'bg-gray-500 text-white'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
                        }`}
                      >
                        {subcategory}
                      </button>
                    ))}
                    </div>
                  </div>
                </div>

                {/* 网站列表 */}
                <div className="h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredSites.map((site) => (
                    <button
                      key={site.id}
                      onClick={() => setSelectedSite(site)}
                      className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                        selectedSite.id === site.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{site.icon}</span>
                          <span className="font-medium">{site.name}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span className={`px-2 py-1 rounded text-xs ${
                            site.category === 'domestic'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              : site.category === 'foreign'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                              : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
                          }`}>
                            {site.category === 'domestic' ? '国内' : site.category === 'foreign' ? '国外' : '区域'}
                          </span>
                          {site.cityTier && (
                            <span className={`px-2 py-1 rounded text-xs ${
                              site.cityTier === 1
                                ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                : site.cityTier === 2
                                ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
                                : site.cityTier === 3
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                                : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            }`}>
                              {site.cityTier}线城市
                            </span>
                          )}
                          <div className="flex">
                            {Array.from({ length: site.popularity }, (_, i) => (
                              <span key={i} className="text-yellow-400 text-xs">⭐</span>
                            ))}
                          </div>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{site.description}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">{site.url}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {site.subcategory}
                        </span>
                        <div className="text-xs">
                          {site.category === 'regional' && site.latencyMultiplier ? (
                            (() => {
                              const expectedLatency = calculateExpectedLatency(site, userCityTier);
                              const description = getLatencyDescription(expectedLatency);
                              return (
                                <div className="flex items-center gap-1">
                                  <span className="text-gray-500">预期延迟:</span>
                                  <span className={`font-medium ${description.color}`}>
                                    {expectedLatency}ms
                                  </span>
                                  <span className={`px-1.5 py-0.5 rounded text-xs ${description.color} bg-opacity-10`} style={{backgroundColor: description.color.replace('text-', 'bg-').replace('-600', '-100')}}>
                                    {description.text}
                                  </span>
                                  <span className="text-gray-400 text-xs">
                                    (从{userCityTier}线城市)
                                  </span>
                                </div>
                              );
                            })()
                          ) : (
                            <span className="text-gray-500">预期延迟: {site.expectedLatency.domestic}ms</span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                  </div>
                </div>
              </div>
            ) : batchMode ? (
              <div>
                {/* 批量测试网站过滤器 */}
                <div className="mb-6 space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">选择要测试的网站</h3>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        已选择 {selectedSites.length} / {filteredSites.length} 个网站
                      </span>
                      <button
                        onClick={toggleSelectAll}
                        className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                      >
                        {selectedSites.length === filteredSites.length ? '取消全选' : '全选'}
                      </button>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setSiteFilter('recommended')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'recommended'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🌟 推荐网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('all')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'all'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🌐 全部网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('domestic')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'domestic'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🇨🇳 国内网站
                    </button>
                    <button
                      onClick={() => setSiteFilter('foreign')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        siteFilter === 'foreign'
                          ? 'bg-purple-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      🌍 国外网站
                    </button>
                  </div>

                  {/* 子分类过滤器 */}
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setSubcategoryFilter('all')}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        subcategoryFilter === 'all'
                          ? 'bg-gray-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
                      }`}
                    >
                      全部分类
                    </button>
                    {getAllSubcategories().map(subcategory => (
                      <button
                        key={subcategory}
                        onClick={() => setSubcategoryFilter(subcategory)}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          subcategoryFilter === subcategory
                            ? 'bg-gray-500 text-white'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
                        }`}
                      >
                        {subcategory}
                      </button>
                    ))}
                  </div>
                </div>

                {/* 批量选择网站列表 */}
                <div className="h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredSites.map((site) => {
                    const isSelected = selectedSites.some(s => s.id === site.id);
                    return (
                      <label
                        key={site.id}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => toggleSiteSelection(site)}
                          className="sr-only"
                        />
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <span className="text-lg mr-2">{site.icon}</span>
                            <span className="font-medium">{site.name}</span>
                            {isSelected && <span className="ml-2 text-blue-500">✓</span>}
                          </div>
                          <div className="flex items-center space-x-1">
                            <span className={`px-2 py-1 rounded text-xs ${
                              site.category === 'domestic'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                : site.category === 'foreign'
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                                : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
                            }`}>
                              {site.category === 'domestic' ? '国内' : site.category === 'foreign' ? '国外' : '区域'}
                            </span>
                            {site.cityTier && (
                              <span className={`px-2 py-1 rounded text-xs ${
                                site.cityTier === 1
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                  : site.cityTier === 2
                                  ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
                                  : site.cityTier === 3
                                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                                  : 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                              }`}>
                                {site.cityTier}线城市
                              </span>
                            )}
                            <div className="flex">
                              {Array.from({ length: site.popularity }, (_, i) => (
                                <span key={i} className="text-yellow-400 text-xs">⭐</span>
                              ))}
                            </div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{site.description}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-2">{site.url}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            {site.subcategory}
                          </span>
                          <div className="text-xs">
                            {site.category === 'regional' && site.latencyMultiplier ? (
                              (() => {
                                const expectedLatency = calculateExpectedLatency(site, userCityTier);
                                const description = getLatencyDescription(expectedLatency);
                                return (
                                  <div className="flex items-center gap-1">
                                    <span className="text-gray-500">预期延迟:</span>
                                    <span className={`font-medium ${description.color}`}>
                                      {expectedLatency}ms
                                    </span>
                                    <span className={`px-1.5 py-0.5 rounded text-xs ${description.color} bg-opacity-10`} style={{backgroundColor: description.color.replace('text-', 'bg-').replace('-600', '-100')}}>
                                      {description.text}
                                    </span>
                                  </div>
                                );
                              })()
                            ) : (
                              <span className="text-gray-500">预期延迟: {site.expectedLatency.domestic}ms</span>
                            )}
                          </div>
                        </div>
                      </label>
                    );
                  })}
                  </div>
                </div>
              </div>
            ) : (
              <input
                type="url"
                value={customUrl}
                onChange={(e) => setCustomUrl(e.target.value)}
                placeholder="请输入要测试的URL，例如：https://www.example.com"
                className={`w-full px-4 py-3 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'} focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
              />
            )}
          </div>

          {/* 环境状态说明 */}
          {isOverseasEnvironment() && (
            <div className={`rounded-lg p-4 mb-6 ${isDarkMode ? 'bg-green-900/20 border border-green-500/30' : 'bg-green-50 border border-green-200'}`}>
              <h4 className="text-md font-semibold mb-3 text-green-600 dark:text-green-400">
                🌍 海外环境检测成功
              </h4>
              <div className="space-y-2 text-sm">
                <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-green-800/30' : 'bg-green-100'}`}>
                  <p className="font-medium text-green-700 dark:text-green-300 mb-2">
                    ✅ 当前运行在海外服务器环境中
                  </p>
                  <div className="text-green-600 dark:text-green-400 space-y-1 text-xs">
                    <p>• <strong>平台状态</strong>: 所有被墙平台现已解锁</p>
                    <p>• <strong>测试范围</strong>: 可访问全球所有网络测试平台</p>
                    <p>• <strong>数据质量</strong>: 获得最全面的网络测试数据</p>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-gray-300 text-xs">
                  🎉 <strong>恭喜</strong>: 现在可以使用全部 <span className="font-bold text-green-600">{PING_PLATFORMS.length}</span> 个测试平台！
                </p>
              </div>
            </div>
          )}

          {/* 平台选择 */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">📡 选择测试平台</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  已选择 {selectedPlatforms.length} / {filteredPlatforms.filter(p => p.available).length} 个平台
                </span>
                <button
                  onClick={() => setSelectedPlatforms(['fast-com', 'aws-ping', 'huawei-cloud'])}
                  className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                  title="基于CSV数据分析的最优组合"
                >
                  🏆 最优组合
                </button>
                <button
                  onClick={toggleSelectAllPlatforms}
                  className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  {selectedPlatforms.length === filteredPlatforms.filter(p => p.available).length ? '取消全选' : '全选可用'}
                </button>
              </div>
            </div>

            {/* 优化组合提示 */}
            {selectedPlatforms.length === 3 &&
             selectedPlatforms.includes('fast-com') &&
             selectedPlatforms.includes('aws-ping') &&
             selectedPlatforms.includes('huawei-cloud') && (
              <div className="mb-4 p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700 rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-2xl">🏆</span>
                  <h4 className="font-bold text-green-800 dark:text-green-300">已选择最优组合 - 国内外均衡表现</h4>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                  <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                    <div className="font-medium text-red-600 dark:text-red-400 mb-1">🎬 Fast.com</div>
                    <div className="text-gray-600 dark:text-gray-400">
                      <div>国内: <span className="font-bold text-green-600">27ms</span></div>
                      <div>国外: <span className="font-bold text-blue-600">225ms</span></div>
                      <div className="text-xs mt-1">Netflix全球CDN</div>
                    </div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                    <div className="font-medium text-orange-600 dark:text-orange-400 mb-1">☁️ AWS CloudPing</div>
                    <div className="text-gray-600 dark:text-gray-400">
                      <div>国内: <span className="font-bold text-green-600">39ms</span></div>
                      <div>国外: <span className="font-bold text-blue-600">288ms</span></div>
                      <div className="text-xs mt-1">国际云服务标准</div>
                    </div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                    <div className="font-medium text-red-600 dark:text-red-400 mb-1">🏢 华为云测速</div>
                    <div className="text-gray-600 dark:text-gray-400">
                      <div>国内: <span className="font-bold text-green-600">55ms</span></div>
                      <div>国外: <span className="font-bold text-blue-600">287ms</span></div>
                      <div className="text-xs mt-1">国内云服务代表</div>
                    </div>
                  </div>
                </div>
                <div className="mt-3 text-sm text-green-700 dark:text-green-400 bg-green-100 dark:bg-green-900/30 p-2 rounded">
                  <strong>✨ 为什么选择这个组合？</strong> 基于CSV测试数据分析，这三个平台在国内外网站测试中表现最均衡，延迟倍数最小，适合全面的网络质量评估。
                </div>
              </div>
            )}

            {/* 平台分类过滤器 */}
            <div className="mb-4">
              <div className="space-y-2">
                {/* 第一行：8个按钮 */}
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setPlatformFilter('available')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'available'
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    ✅ 可用平台 ({PING_PLATFORMS.filter(p => p.available).length})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('all')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'all'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🌐 全部平台 ({PING_PLATFORMS.length})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('domestic')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'domestic'
                        ? 'bg-red-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🇨🇳 国内平台 ({getFilteredPlatforms().length > 0 && platformFilter === 'domestic' ? getFilteredPlatforms().length :
                      PING_PLATFORMS.filter(p =>
                        ['itdog', '17ce', 'boce', 'chinaz', 'webkaka', 'ce8', 'ipip', 'ping-pe', 'alicloud', 'tencent-cloud', 'huawei-cloud',
                         'tool-lu', 'ip138', 'ip-cn', 'linkwan', 'netsh', 'ping-chinaz', 'webluker', 'mmtrix', 'netspeedtestmaster', 'speedcn',
                         'bootcdn', 'staticfile', 'baidu-cdn', 'mtr-sh', 'traceroute-online', 'nslookup-io', 'whois-net', 'dig-web'].includes(p.id)
                      ).length})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('foreign')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'foreign'
                        ? 'bg-purple-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🌍 国外平台 ({getFilteredPlatforms().length > 0 && platformFilter === 'foreign' ? getFilteredPlatforms().length :
                      PING_PLATFORMS.filter(p =>
                        ['globalping', 'keycdn', 'pingdom', 'gtmetrix', 'uptrends', 'dotcom-tools', 'site24x7', 'host-tracker',
                         'whatismyip', 'network-tools', 'ping-eu', 'just-ping', 'ca-app-synthetic', 'monitis', 'alertsite',
                         'keynote', 'gomez', 'neustar', 'cedexis', 'thousandeyes', 'catchpoint', 'broadband-now', 'speedof-me', 'testmy-net'].includes(p.id)
                      ).length})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('cloud')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'cloud'
                        ? 'bg-cyan-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    ☁️ 云服务 ({getFilteredPlatforms().length > 0 && platformFilter === 'cloud' ? getFilteredPlatforms().length : 22})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('isp')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'isp'
                        ? 'bg-indigo-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    📶 运营商 ({getFilteredPlatforms().length > 0 && platformFilter === 'isp' ? getFilteredPlatforms().length : 11})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('cdn')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'cdn'
                        ? 'bg-orange-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    📦 CDN ({getFilteredPlatforms().length > 0 && platformFilter === 'cdn' ? getFilteredPlatforms().length : 12})
                  </button>
                </div>

                {/* 第二行：8个按钮 */}
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setPlatformFilter('tools')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'tools'
                        ? 'bg-teal-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🔧 工具 ({getFilteredPlatforms().length > 0 && platformFilter === 'tools' ? getFilteredPlatforms().length : 15})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('gaming')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'gaming'
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🎮 游戏 ({getFilteredPlatforms().length > 0 && platformFilter === 'gaming' ? getFilteredPlatforms().length : 4})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('social')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'social'
                        ? 'bg-pink-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    💬 社交 ({getFilteredPlatforms().length > 0 && platformFilter === 'social' ? getFilteredPlatforms().length : 7})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('streaming')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'streaming'
                        ? 'bg-red-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    📺 流媒体 ({getFilteredPlatforms().length > 0 && platformFilter === 'streaming' ? getFilteredPlatforms().length : 4})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('enterprise')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'enterprise'
                        ? 'bg-purple-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🏢 企业级 ({getFilteredPlatforms().length > 0 && platformFilter === 'enterprise' ? getFilteredPlatforms().length : 27})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('ecommerce')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'ecommerce'
                        ? 'bg-yellow-500 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🛒 电商 ({getFilteredPlatforms().length > 0 && platformFilter === 'ecommerce' ? getFilteredPlatforms().length : 11})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('finance')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'finance'
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    💰 金融 ({getFilteredPlatforms().length > 0 && platformFilter === 'finance' ? getFilteredPlatforms().length : 13})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('music')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'music'
                        ? 'bg-purple-400 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🎵 音乐 ({getFilteredPlatforms().length > 0 && platformFilter === 'music' ? getFilteredPlatforms().length : 6})
                  </button>
                  <button
                    onClick={() => setPlatformFilter('lifestyle')}
                    className={`px-3 py-1 rounded-full text-sm transition-colors ${
                      platformFilter === 'lifestyle'
                        ? 'bg-indigo-400 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    🏠 生活 ({getFilteredPlatforms().length > 0 && platformFilter === 'lifestyle' ? getFilteredPlatforms().length : 21})
                  </button>
                </div>
              </div>
            </div>

            {/* 平台列表 */}
            <div className="h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                {filteredPlatforms.map((platform) => (
                  <label
                    key={platform.id}
                    className={`flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                      selectedPlatforms.includes(platform.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    } ${!platform.available ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedPlatforms.includes(platform.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedPlatforms([...selectedPlatforms, platform.id]);
                        } else {
                          setSelectedPlatforms(selectedPlatforms.filter(id => id !== platform.id));
                        }
                      }}
                      disabled={!platform.available}
                      className="mr-3"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-1">
                        <span className="text-lg mr-2 flex-shrink-0">{platform.icon}</span>
                        <span className="font-medium text-sm truncate">{platform.name}</span>
                        {!platform.available && (
                          <span className="ml-1 px-1 py-0.5 bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 text-xs rounded flex-shrink-0">
                            被墙
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">{platform.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* 平台统计信息 */}
            <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center text-sm">
                <div>
                  <div className="font-bold text-green-600">{PING_PLATFORMS.filter(p => p.available).length}</div>
                  <div className="text-gray-600 dark:text-gray-400">可用平台</div>
                </div>
                <div>
                  <div className="font-bold text-red-600">{PING_PLATFORMS.filter(p => !p.available).length}</div>
                  <div className="text-gray-600 dark:text-gray-400">被墙平台</div>
                </div>
                <div>
                  <div className="font-bold text-blue-600">{selectedPlatforms.length}</div>
                  <div className="text-gray-600 dark:text-gray-400">已选择</div>
                </div>
                <div>
                  <div className="font-bold text-purple-600">{PING_PLATFORMS.length}</div>
                  <div className="text-gray-600 dark:text-gray-400">总计</div>
                </div>
              </div>
            </div>
          </div>

          {/* 开始测试按钮 */}
          <button
            data-test-button="true"
            onClick={batchMode ? runBatchTests : runAllTests}
            disabled={
              (batchMode ? isBatchTesting : isTestingAll) ||
              selectedPlatforms.length === 0 ||
              (batchMode && selectedSites.length === 0) ||
              (!batchMode && !useCustomUrl && !selectedSite) ||
              (!batchMode && useCustomUrl && !customUrl)
            }
            className={`w-full py-4 px-6 rounded-lg font-semibold text-white transition-all duration-200 ${
              (batchMode ? isBatchTesting : isTestingAll) ||
              selectedPlatforms.length === 0 ||
              (batchMode && selectedSites.length === 0) ||
              (!batchMode && !useCustomUrl && !selectedSite) ||
              (!batchMode && useCustomUrl && !customUrl)
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
            }`}
          >
            {batchMode ? (
              isBatchTesting ? '🔄 批量测试进行中...' : `🚀 开始批量测试 (${selectedSites.length}个网站)`
            ) : (
              isTestingAll ? '🔄 测试进行中...' : '🚀 开始全平台延迟测试'
            )}
          </button>
        </div>

        {/* 单个测试结果区域 */}
        {!batchMode && Object.keys(testResults).length > 0 && (
          <div className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h2 className="text-xl font-semibold mb-4">📊 测试结果</h2>

            {/* 目标信息 */}
            <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium mb-2">测试目标</h3>
              <p className="text-sm">
                {useCustomUrl ? customUrl : `${selectedSite.name} (${selectedSite.url})`}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                测试时间: {new Date().toLocaleString('zh-CN')}
              </p>
            </div>

            {/* 结果表格 */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className={`border-b ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                    <th className="text-left py-3 px-4">平台</th>
                    <th className="text-left py-3 px-4">延迟</th>
                    <th className="text-left py-3 px-4">状态</th>
                    <th className="text-left py-3 px-4">评级</th>
                  </tr>
                </thead>
                <tbody>
                  {PING_PLATFORMS.filter(p => selectedPlatforms.includes(p.id)).map((platform) => {
                    const result = testResults[platform.id];
                    if (!result) return null;

                    const latencyRating = result.latency ? (
                      result.latency < 50 ? '优秀' :
                      result.latency < 100 ? '良好' :
                      result.latency < 200 ? '一般' : '较慢'
                    ) : '-';

                    return (
                      <tr key={platform.id} className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-100'}`}>
                        <td className="py-4 px-4">
                          <div className="flex items-center">
                            <span className="text-lg mr-2">{platform.icon}</span>
                            <div>
                              <div className="font-medium">{platform.name}</div>
                              <div className="text-sm text-gray-500">{platform.description}</div>
                            </div>
                          </div>
                        </td>
                        <td className={`py-4 px-4 font-mono text-lg ${getLatencyColor(result.latency, result.status)}`}>
                          {formatLatency(result)}
                        </td>
                        <td className="py-4 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${
                            result.status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            result.status === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            result.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {result.status === 'success' ? '成功' :
                             result.status === 'error' ? '失败' :
                             result.status === 'pending' ? '测试中' : '未知'}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${
                            latencyRating === '优秀' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            latencyRating === '良好' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                            latencyRating === '一般' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            latencyRating === '较慢' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                          }`}>
                            {latencyRating}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* 统计信息 */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
              {(() => {
                const successResults = Object.values(testResults).filter(r => r.status === 'success' && r.latency !== null);
                const latencies = successResults.map(r => r.latency!);
                const avgLatency = latencies.length > 0 ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length) : 0;
                const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0;
                const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0;
                const successRate = Object.values(testResults).length > 0 ?
                  Math.round((successResults.length / Object.values(testResults).length) * 100) : 0;

                return (
                  <>
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{avgLatency}ms</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">平均延迟</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{minLatency}ms</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">最低延迟</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{maxLatency}ms</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">最高延迟</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{successRate}%</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">成功率</div>
                    </div>
                  </>
                );
              })()}
            </div>

            {/* 导出按钮 */}
            <div className="mt-6 flex space-x-4">
              <button
                onClick={() => {
                  const data = {
                    target: useCustomUrl ? customUrl : selectedSite.url,
                    timestamp: new Date().toISOString(),
                    results: testResults
                  };
                  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `ping-test-${Date.now()}.json`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                📥 导出JSON
              </button>
              <button
                onClick={() => {
                  const csvContent = [
                    ['平台', '延迟(ms)', '状态', '时间戳'].join(','),
                    ...Object.entries(testResults).map(([platformId, result]) => {
                      const platform = PING_PLATFORMS.find(p => p.id === platformId);
                      return [
                        platform?.name || platformId,
                        result.latency || 'N/A',
                        result.status,
                        result.timestamp || ''
                      ].join(',');
                    })
                  ].join('\n');

                  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `ping-test-${Date.now()}.csv`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                📊 导出CSV
              </button>
            </div>
          </div>
        )}

        {/* 单个测试延迟对比图表 */}
        {!batchMode && Object.keys(testResults).length > 0 && (
          <div data-chart-section="true" className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <LatencyChart
              results={testResults}
              platforms={PING_PLATFORMS}
              targetUrl={useCustomUrl ? customUrl : selectedSite.url}
              isDarkMode={isDarkMode}
              chartType={chartType}
              onChartTypeChange={setChartType}
            />
          </div>
        )}

        {/* 批量测试结果区域 */}
        {batchMode && Object.keys(batchResults).length > 0 && (
          <div data-chart-section="true" className={`rounded-lg shadow-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h2 className="text-xl font-semibold mb-4">📊 批量测试结果</h2>

            {/* 批量测试概览 */}
            <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium mb-2">测试概览</h3>
              <p className="text-sm">
                测试网站数量: {Object.keys(batchResults).length} |
                测试平台: {selectedPlatforms.length} |
                测试时间: {new Date().toLocaleString('zh-CN')}
              </p>
            </div>

            {/* 批量结果表格 */}
            <div className="space-y-6">
              {Object.entries(batchResults).map(([siteId, siteResults]) => {
                const site = selectedSites.find(s => s.id === siteId);
                if (!site) return null;

                const successResults = Object.values(siteResults).filter(r => r.status === 'success' && r.latency !== null);
                const latencies = successResults.map(r => r.latency!);
                const avgLatency = latencies.length > 0 ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length) : 0;
                const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0;
                const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0;

                return (
                  <div key={siteId} className={`border rounded-lg p-4 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{site.icon}</span>
                        <div>
                          <h4 className="font-medium">{site.name}</h4>
                          <p className="text-sm text-gray-500">{site.url}</p>
                        </div>
                      </div>
                      <div className="flex space-x-4 text-sm">
                        <div className="text-center">
                          <div className="font-bold text-blue-600">{avgLatency}ms</div>
                          <div className="text-gray-500">平均</div>
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-green-600">{minLatency}ms</div>
                          <div className="text-gray-500">最低</div>
                        </div>
                        <div className="text-center">
                          <div className="font-bold text-red-600">{maxLatency}ms</div>
                          <div className="text-gray-500">最高</div>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {PING_PLATFORMS.filter(p => selectedPlatforms.includes(p.id)).map(platform => {
                        const result = siteResults[platform.id];
                        if (!result) return null;

                        return (
                          <div key={platform.id} className={`p-3 rounded border ${isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'}`}>
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium">{platform.icon} {platform.name}</span>
                              <span className={`text-sm px-2 py-1 rounded ${
                                result.status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                              }`}>
                                {result.status === 'success' ? '成功' : '失败'}
                              </span>
                            </div>
                            <div className={`text-lg font-mono ${getLatencyColor(result.latency, result.status)}`}>
                              {formatLatency(result)}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 批量测试延迟对比图表 */}
            <div className="mt-8">
              <h3 className="text-lg font-medium mb-4">📈 延迟对比图表</h3>
              {Object.entries(batchResults).map(([siteId, siteResults]) => {
                const site = selectedSites.find(s => s.id === siteId);
                if (!site) return null;

                return (
                  <div key={siteId} className="mb-6">
                    <h4 className="text-md font-medium mb-3">{site.name}</h4>
                    <LatencyChart
                      results={siteResults}
                      platforms={PING_PLATFORMS}
                      targetUrl={site.url}
                      isDarkMode={isDarkMode}
                      chartType={chartType}
                      onChartTypeChange={setChartType}
                    />
                  </div>
                );
              })}
            </div>

            {/* 批量测试导出按钮 */}
            <div className="mt-6 flex space-x-4">
              <button
                onClick={() => {
                  const data = {
                    testType: 'batch',
                    sites: selectedSites.map(site => ({ id: site.id, name: site.name, url: site.url })),
                    platforms: selectedPlatforms,
                    timestamp: new Date().toISOString(),
                    results: batchResults
                  };
                  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `batch-ping-test-${Date.now()}.json`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                📥 导出批量结果JSON
              </button>
              <button
                onClick={() => {
                  const csvRows = ['网站,平台,延迟(ms),状态,时间戳'];
                  Object.entries(batchResults).forEach(([siteId, siteResults]) => {
                    const site = selectedSites.find(s => s.id === siteId);
                    Object.entries(siteResults).forEach(([platformId, result]) => {
                      const platform = PING_PLATFORMS.find(p => p.id === platformId);
                      csvRows.push([
                        site?.name || siteId,
                        platform?.name || platformId,
                        result.latency || 'N/A',
                        result.status,
                        result.timestamp || ''
                      ].join(','));
                    });
                  });

                  const blob = new Blob([csvRows.join('\n')], { type: 'text/csv;charset=utf-8;' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `batch-ping-test-${Date.now()}.csv`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                📊 导出批量结果CSV
              </button>
            </div>
          </div>
        )}

        {/* 说明信息 */}
        <div className={`rounded-lg shadow-lg p-6 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <h2 className="text-xl font-semibold mb-4">ℹ️ 使用说明</h2>
          <div className="space-y-4 text-sm">
            <div>
              <h3 className="font-medium mb-2">🎯 测试原理</h3>
              <p className="text-gray-600 dark:text-gray-400">
                本工具通过多个ping平台同时测试目标网站的网络延迟，提供全面的网络连通性分析。
                不同平台使用不同的测试节点和方法，结果可能存在差异。
              </p>
            </div>
            <div>
              <h3 className="font-medium mb-2">🚀 功能特色</h3>
              <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                <li>• <strong>单个测试</strong>: 针对单个网站进行多平台延迟对比</li>
                <li>• <strong>批量测试</strong>: 一键测试多个网站，生成综合报告</li>
                <li>• <strong>可视化图表</strong>: 柱状图、折线图、饼图多种展示方式</li>
                <li>• <strong>数据导出</strong>: 支持JSON、CSV格式导出测试结果</li>
                <li>• <strong>智能分类</strong>: 按网站类型、地区、流行度智能分类</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">🚀 终极无限扩展 (总计{PING_PLATFORMS.length}个平台！)</h3>
              <div className="space-y-3">
                <div className="bg-gradient-to-r from-purple-50 via-pink-50 to-red-50 dark:from-purple-900/20 dark:via-pink-900/20 dark:to-red-900/20 p-4 rounded-lg border-2 border-purple-200 dark:border-purple-700">
                  <h4 className="font-bold text-xl text-purple-600 dark:text-purple-400 mb-3">🎉 史上最疯狂的平台扩展！总共{PING_PLATFORMS.length}个平台！</h4>
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-sm">
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                      <div className="font-bold text-green-600">🇨🇳 国内可用</div>
                      <div className="text-3xl font-bold text-green-500">{PING_PLATFORMS.filter(p => p.available).length}</div>
                      <div className="text-xs text-green-400">100%可用</div>
                    </div>
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                      <div className="font-bold text-blue-600">☁️ 云服务</div>
                      <div className="text-3xl font-bold text-blue-500">30+</div>
                      <div className="text-xs text-blue-400">全球覆盖</div>
                    </div>
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                      <div className="font-bold text-purple-600">🌍 国外平台</div>
                      <div className="text-3xl font-bold text-purple-500">{PING_PLATFORMS.filter(p => !p.available).length}</div>
                      <div className="text-xs text-purple-400">专业级</div>
                    </div>
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                      <div className="font-bold text-orange-600">🎯 分类系统</div>
                      <div className="text-3xl font-bold text-orange-500">16</div>
                      <div className="text-xs text-orange-400">精准定位</div>
                    </div>
                    <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg shadow">
                      <div className="font-bold text-red-600">📈 增长率</div>
                      <div className="text-3xl font-bold text-red-500">{Math.round((PING_PLATFORMS.length - 6) / 6 * 100)}%</div>
                      <div className="text-xs text-red-400">史诗级</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-green-600 mb-2">🇨🇳 国内平台 ({PING_PLATFORMS.filter(p => p.available).length}个，全部可用)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3 text-gray-600 dark:text-gray-400 text-sm ml-4">
                    <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded">
                      <p className="font-medium mb-1 text-green-700 dark:text-green-400">🏦 金融银行:</p>
                      <ul className="space-y-0.5">
                        <li>• 工商/建设/农业/中国银行</li>
                        <li>• 招商银行/支付宝/微信支付</li>
                        <li>• 火币/OKX - 数字货币</li>
                      </ul>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                      <p className="font-medium mb-1 text-blue-700 dark:text-blue-400">🛒 电商购物:</p>
                      <ul className="space-y-0.5">
                        <li>• 淘宝/天猫/京东/拼多多</li>
                        <li>• 全球速卖通</li>
                        <li>• 美团/饿了么 - 外卖</li>
                      </ul>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-2 rounded">
                      <p className="font-medium mb-1 text-purple-700 dark:text-purple-400">🎵 娱乐媒体:</p>
                      <ul className="space-y-0.5">
                        <li>• 网易云/QQ/酷狗/酷我音乐</li>
                        <li>• 爱奇艺/优酷/腾讯/芒果TV</li>
                        <li>• B站/抖音/小红书</li>
                      </ul>
                    </div>
                    <div className="bg-orange-50 dark:bg-orange-900/20 p-2 rounded">
                      <p className="font-medium mb-1 text-orange-700 dark:text-orange-400">🏠 生活服务:</p>
                      <ul className="space-y-0.5">
                        <li>• 滴滴/12306/携程/去哪儿</li>
                        <li>• 百度/高德/腾讯地图</li>
                        <li>• 钉钉/飞书/企业微信</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-blue-600 mb-1">☁️ 云服务平台 (21个，全部可用)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-gray-600 dark:text-gray-400 text-sm ml-4">
                    <div>
                      <p className="font-medium mb-1">主流云服务:</p>
                      <ul className="space-y-0.5">
                        <li>• AWS CloudPing - 亚马逊云</li>
                        <li>• Azure Speed - 微软云</li>
                        <li>• GCP Network - 谷歌云</li>
                        <li>• 阿里云/腾讯云/华为云</li>
                        <li>• Cloudflare - 全球CDN</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">新增云服务:</p>
                      <ul className="space-y-0.5">
                        <li>• DigitalOcean - 开发者云</li>
                        <li>• Linode - 高性能云</li>
                        <li>• Vultr - 全球云服务</li>
                        <li>• Hetzner - 欧洲云服务</li>
                        <li>• OVH - 法国云服务</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">CDN和测速:</p>
                      <ul className="space-y-0.5">
                        <li>• Cloudflare/jsDelivr/UNPKG</li>
                        <li>• cdnjs/BootCDN/七牛云/百度</li>
                        <li>• Fastly/MaxCDN/BunnyCDN</li>
                        <li>• Speedtest.net/Fast.com</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-purple-600 mb-1">🎮 游戏和流媒体 (15+个)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-gray-600 dark:text-gray-400 text-sm ml-4">
                    <div>
                      <p className="font-medium mb-1">游戏平台:</p>
                      <ul className="space-y-0.5">
                        <li>• Steam - PC游戏平台</li>
                        <li>• Xbox Live - 微软游戏</li>
                        <li>• PSN - 索尼PlayStation</li>
                        <li>• Nintendo - 任天堂</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">流媒体:</p>
                      <ul className="space-y-0.5">
                        <li>• YouTube - 视频平台</li>
                        <li>• Twitch - 游戏直播</li>
                        <li>• Bilibili - B站</li>
                        <li>• Netflix (Fast.com)</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">社交媒体:</p>
                      <ul className="space-y-0.5">
                        <li>• Discord/Telegram - 聊天</li>
                        <li>• 微信/QQ - 国内社交</li>
                        <li>• 微信支付/支付宝 - 支付</li>
                        <li>• 百度网盘/阿里云盘 - 存储</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-red-600 mb-1">🌍 国外专业平台 (50+个，大部分被墙)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-gray-600 dark:text-gray-400 text-sm ml-4">
                    <div>
                      <p className="font-medium mb-1">企业级监控:</p>
                      <ul className="space-y-0.5">
                        <li>• Globalping - 200+全球节点</li>
                        <li>• ThousandEyes/Catchpoint</li>
                        <li>• PingPlotter/PRTG/SolarWinds</li>
                        <li>• Nagios/Zabbix/Icinga</li>
                        <li>• Observium/LibreNMS/Pandora</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">区域服务:</p>
                      <ul className="space-y-0.5">
                        <li>• Ping.Asia - 亚洲节点</li>
                        <li>• Ping.America - 美洲节点</li>
                        <li>• Ping.Africa - 非洲节点</li>
                        <li>• Oceania Ping - 大洋洲</li>
                        <li>• Ping.eu - 欧洲节点</li>
                      </ul>
                    </div>
                    <div>
                      <p className="font-medium mb-1">专业服务:</p>
                      <ul className="space-y-0.5">
                        <li>• Pingdom/GTmetrix - 性能</li>
                        <li>• KeyCDN/Network-Tools</li>
                        <li>• PayPal/Stripe - 支付</li>
                        <li>• Gmail/Outlook - 邮件</li>
                        <li>• Dropbox/OneDrive - 存储</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-2">⚠️ 注意事项</h3>
              <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                <li>• 部分国外平台在中国大陆地区可能无法访问</li>
                <li>• 测试结果受网络环境、时间段等因素影响</li>
                <li>• 建议多次测试取平均值以获得更准确的结果</li>
                <li>• 延迟评级：优秀(&lt;50ms)、良好(50-100ms)、一般(100-200ms)、较慢(&gt;200ms)</li>
                <li>• 批量测试建议选择5-10个网站以获得最佳性能</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">📈 使用建议</h3>
              <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                <li>• 首次使用建议选择"推荐网站"进行测试</li>
                <li>• 对比国内外网站延迟差异，了解网络连通性</li>
                <li>• 使用批量测试功能对多个网站进行横向对比</li>
                <li>• 定期测试可以监控网络质量变化趋势</li>
                <li>• 导出数据进行长期分析和记录</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* 被墙平台说明 - 移动到页面底部 */}
      {!isOverseasEnvironment() && PING_PLATFORMS.filter(p => !p.available).length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
          <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-red-900/20 border border-red-500/30' : 'bg-red-50 border border-red-200'}`}>
            <h4 className="text-md font-semibold mb-3 text-red-600 dark:text-red-400">
              🚫 关于被墙平台的重要说明
            </h4>
            <div className="space-y-2 text-sm">
              <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-red-800/30' : 'bg-red-100'}`}>
                <p className="font-medium text-red-700 dark:text-red-300 mb-2">
                  ⚠️ 为什么开启代理后“被墙平台”仍然无法使用？
                </p>
                <div className="text-red-600 dark:text-red-400 space-y-1 text-xs">
                  <p>• <strong>网络路径</strong>: 浏览器 → 服务器 → 被墙平台</p>
                  <p>• <strong>VPN限制</strong>: VPN/代理只影响浏览器，不影响服务器端请求</p>
                  <p>• <strong>服务器环境</strong>: 服务器在国内，无法访问被墙平台API</p>
                </div>
              </div>
              <p className="text-gray-700 dark:text-gray-300 text-xs">
                💡 <strong>建议</strong>: 使用 <span className="font-bold text-green-600">{PING_PLATFORMS.filter(p => p.available).length}</span> 个可用平台，同样能提供准确的测试结果。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 浮动按钮组 */}
      <div className="fixed bottom-6 right-6 flex flex-col gap-3 z-50">
        {/* 回到顶部按钮 */}
        <button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className={`w-12 h-12 rounded-full shadow-lg transition-all duration-300 hover:scale-110 ${
            isDarkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
              : 'bg-white hover:bg-gray-50 text-gray-700 border border-gray-200'
          }`}
          title="回到顶部"
        >
          <span className="text-lg">⬆️</span>
        </button>

        {/* 定位到图表按钮 */}
        <button
          onClick={() => {
            const chartSection = document.querySelector('[data-chart-section="true"]');
            if (chartSection) {
              chartSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }}
          className={`w-12 h-12 rounded-full shadow-lg transition-all duration-300 hover:scale-110 ${
            isDarkMode
              ? 'bg-green-600 hover:bg-green-500 text-white'
              : 'bg-green-500 hover:bg-green-600 text-white'
          }`}
          title="定位到延迟对比分析图表"
        >
          <span className="text-lg">📊</span>
        </button>

        {/* 定位到开始测试按钮 */}
        <button
          onClick={() => {
            const testButton = document.querySelector('[data-test-button="true"]');
            if (testButton) {
              testButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
          }}
          className={`w-12 h-12 rounded-full shadow-lg transition-all duration-300 hover:scale-110 ${
            isDarkMode
              ? 'bg-blue-600 hover:bg-blue-500 text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
          title="定位到开始测试按钮"
        >
          <span className="text-lg">🚀</span>
        </button>
      </div>
    </div>
  );
}
