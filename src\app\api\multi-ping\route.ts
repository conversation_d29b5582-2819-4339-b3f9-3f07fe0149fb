import { NextRequest, NextResponse } from 'next/server';

// 检测是否运行在海外环境
function isOverseasEnvironment(): boolean {
  // 检查环境变量
  if (process.env.NEXT_PUBLIC_IS_OVERSEAS === 'true') {
    return true;
  }

  // 检查是否在 Vercel 环境
  if (process.env.VERCEL === '1') {
    return true;
  }

  // 默认返回 false (国内环境)
  return false;
}

// 被墙平台列表 (在海外环境中这些平台可用)
const BLOCKED_PLATFORMS = [
  'globalping', 'keycdn', 'pingdom', 'gtmetrix', 'uptrends', 'dotcom-tools',
  'site24x7', 'host-tracker', 'whatismyip', 'network-tools', 'ping-eu',
  'just-ping', 'ca-app-synthetic', 'monitis', 'alertsite', 'keynote',
  'gomez', 'neustar', 'cedexis', 'thousandeyes', 'catchpoint', 'broadband-now',
  'speedof-me', 'testmy-net', 'pingplotter', 'paessler-prtg', 'solarwinds', 'nagios',
  'zabbix', 'icinga', 'observium', 'librenms', 'pandora-fms', 'cacti',
  'ping-asia', 'ping-america', 'ping-africa', 'oceania-ping', 'fastly-speed',
  'maxcdn', 'bunnycdn', 'stackpath', 'youtube-ping', 'twitch-ping',
  'discord-ping', 'telegram-ping', 'shopify-ping', 'woocommerce-ping',
  'gitlab-ping', 'bitbucket-ping', 'gmail-ping', 'outlook-ping',
  'duckduckgo-ping', 'yandex-ping', 'reuters-ping', 'ap-ping',
  'paypal-ping', 'stripe-ping', 'coursera-ping', 'edx-ping', 'dropbox-ping',
  'onedrive-ping', 'amazon-ping', 'ebay-ping', 'uber-ping', 'google-maps-ping',
  'spotify-ping', 'apple-music-ping', 'netflix-ping', 'hulu-ping',
  'slack-ping', 'teams-ping', 'zoom-ping', 'facebook-ping', 'twitter-ping',
  'instagram-ping', 'linkedin-ping', 'tiktok-ping', 'github-pages-ping',
  'netlify-ping', 'heroku-ping', 'railway-ping', 'render-ping',
  'amazon-cloudfront-ping', 'azure-cdn-ping', 'google-cloud-cdn-ping', 'keycdn-ping',
  'binance-ping', 'coinbase-ping', 'epic-games-ping', 'origin-ping',
  'uplay-ping', 'battle-net-ping', 'stackoverflow-ping', 'codepen-ping',
  'jsfiddle-ping', 'replit-ping', 'codesandbox-ping', 'salesforce-ping',
  'oracle-ping', 'sap-ping', 'ibm-ping', 'apple-ping', 'microsoft-ping',
  'google-ping', 'meta-ping', 'tesla-ping', 'wish-ping', 'etsy-ping',
  'canva-ping', 'figma-ping', 'notion-ping', 'airtable-ping'
];

// 获取平台可用性
function getPlatformAvailability(platformId: string): boolean {
  if (BLOCKED_PLATFORMS.includes(platformId)) {
    return isOverseasEnvironment();
  }
  return true;
}

// 定义ping平台接口
interface PingPlatformConfig {
  id: string;
  name: string;
  available: boolean;
  handler: (url: string) => Promise<PingResult>;
}

// 定义统一的ping结果接口
interface PingResult {
  platform: string;
  latency: number | null;
  status: 'success' | 'error' | 'timeout';
  error?: string;
  timestamp: string;
  metadata?: any;
}

// Vercel Edge API调用
async function callVercelEdgeAPI(url: string): Promise<PingResult> {
  try {
    const response = await fetch(`${process.env.VERCEL_URL || 'http://localhost:3000'}/api/edge-ping?target=${encodeURIComponent(url)}`, {
      method: 'GET',
      headers: {
        'User-Agent': 'Multi-Ping-Service/1.0'
      }
    });

    const data = await response.json();
    
    return {
      platform: 'vercel-edge',
      latency: data.success ? data.latency : null,
      status: data.success ? 'success' : 'error',
      error: data.success ? undefined : data.error,
      timestamp: new Date().toISOString(),
      metadata: {
        edge: data.edge,
        originalLatency: data.testResult?.originalLatency
      }
    };
  } catch (error) {
    return {
      platform: 'vercel-edge',
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }
}

// ITDOG API调用（模拟实现）
async function callITDogAPI(url: string): Promise<PingResult> {
  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // 模拟ITDOG的延迟特性（通常对国内网站延迟较低）
    const domain = new URL(url).hostname.toLowerCase();
    const isDomestic = domain.includes('.cn') || 
                      domain.includes('baidu') || 
                      domain.includes('taobao') || 
                      domain.includes('qq.com') || 
                      domain.includes('jd.com') ||
                      domain.includes('weibo.com');
    
    let baseLatency = isDomestic ? 20 + Math.random() * 80 : 150 + Math.random() * 300;
    
    // ITDOG通常延迟较低，应用0.9倍数
    baseLatency *= 0.9;
    
    return {
      platform: 'itdog',
      latency: Math.round(baseLatency),
      status: 'success',
      timestamp: new Date().toISOString(),
      metadata: {
        isDomestic,
        testNodes: ['北京联通', '上海电信', '广州移动']
      }
    };
  } catch (error) {
    return {
      platform: 'itdog',
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Network error',
      timestamp: new Date().toISOString()
    };
  }
}

// 17CE API调用（模拟实现）
async function call17CEAPI(url: string): Promise<PingResult> {
  try {
    await new Promise(resolve => setTimeout(resolve, 1200 + Math.random() * 1800));
    
    const domain = new URL(url).hostname.toLowerCase();
    const isDomestic = domain.includes('.cn') || 
                      domain.includes('baidu') || 
                      domain.includes('taobao') || 
                      domain.includes('qq.com') || 
                      domain.includes('jd.com') ||
                      domain.includes('weibo.com');
    
    let baseLatency = isDomestic ? 25 + Math.random() * 75 : 180 + Math.random() * 320;
    
    // 17CE延迟稍高，应用1.1倍数
    baseLatency *= 1.1;
    
    return {
      platform: '17ce',
      latency: Math.round(baseLatency),
      status: 'success',
      timestamp: new Date().toISOString(),
      metadata: {
        isDomestic,
        testNodes: ['北京', '上海', '广州', '深圳', '杭州']
      }
    };
  } catch (error) {
    return {
      platform: '17ce',
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Network error',
      timestamp: new Date().toISOString()
    };
  }
}

// BOCE API调用（模拟实现）
async function callBOCEAPI(url: string): Promise<PingResult> {
  try {
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 2200));
    
    const domain = new URL(url).hostname.toLowerCase();
    const isDomestic = domain.includes('.cn') || 
                      domain.includes('baidu') || 
                      domain.includes('taobao') || 
                      domain.includes('qq.com') || 
                      domain.includes('jd.com') ||
                      domain.includes('weibo.com');
    
    let baseLatency = isDomestic ? 22 + Math.random() * 78 : 160 + Math.random() * 280;
    
    // BOCE延迟中等，应用0.95倍数
    baseLatency *= 0.95;
    
    return {
      platform: 'boce',
      latency: Math.round(baseLatency),
      status: 'success',
      timestamp: new Date().toISOString(),
      metadata: {
        isDomestic,
        testNodes: ['多线BGP', '电信', '联通', '移动']
      }
    };
  } catch (error) {
    return {
      platform: 'boce',
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Network error',
      timestamp: new Date().toISOString()
    };
  }
}

// Globalping API调用（模拟实现，实际在国内被墙）
async function callGlobalpingAPI(url: string): Promise<PingResult> {
  try {
    // 模拟被墙的情况
    if (Math.random() < 0.8) {
      throw new Error('Connection timeout - service may be blocked');
    }
    
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000));
    
    const baseLatency = 200 + Math.random() * 400; // 国外节点延迟较高
    
    return {
      platform: 'globalping',
      latency: Math.round(baseLatency),
      status: 'success',
      timestamp: new Date().toISOString(),
      metadata: {
        testNodes: ['US-East', 'EU-West', 'Asia-Pacific']
      }
    };
  } catch (error) {
    return {
      platform: 'globalping',
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Service unavailable',
      timestamp: new Date().toISOString()
    };
  }
}

// KeyCDN API调用（模拟实现，实际在国内被墙）
async function callKeyCDNAPI(url: string): Promise<PingResult> {
  try {
    // 模拟被墙的情况
    if (Math.random() < 0.7) {
      throw new Error('Connection refused - service may be blocked');
    }
    
    await new Promise(resolve => setTimeout(resolve, 2500 + Math.random() * 2500));
    
    const baseLatency = 180 + Math.random() * 350;
    
    return {
      platform: 'keycdn',
      latency: Math.round(baseLatency),
      status: 'success',
      timestamp: new Date().toISOString(),
      metadata: {
        testNodes: ['CDN Edge Locations']
      }
    };
  } catch (error) {
    return {
      platform: 'keycdn',
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Service unavailable',
      timestamp: new Date().toISOString()
    };
  }
}

// 通用模拟API调用函数
async function callGenericAPI(platformId: string, platformName: string, url: string, available: boolean = true): Promise<PingResult> {
  try {
    if (!available && Math.random() < 0.8) {
      throw new Error('Service unavailable - may be blocked in your region');
    }

    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 2200));

    const domain = new URL(url).hostname.toLowerCase();
    const isDomestic = domain.includes('.cn') ||
                      domain.includes('baidu') ||
                      domain.includes('taobao') ||
                      domain.includes('qq.com') ||
                      domain.includes('jd.com') ||
                      domain.includes('weibo.com');

    let baseLatency = isDomestic ? 20 + Math.random() * 80 : 150 + Math.random() * 300;

    // 不同平台的延迟特性调整
    switch (platformId) {
      case 'itdog':
      case 'chinaz':
      case 'webkaka':
        baseLatency *= 0.9; // 国内平台延迟较低
        break;
      case '17ce':
      case 'ce8':
        baseLatency *= 1.1; // 部分平台延迟稍高
        break;
      case 'globalping':
      case 'keycdn':
      case 'pingdom':
        baseLatency *= 1.3; // 国外平台延迟较高
        break;
      case 'cloudflare':
      case 'aws-ping':
        baseLatency *= 0.8; // CDN平台延迟较低
        break;
      case 'speedtest':
      case 'fast-com':
        baseLatency *= 0.85; // 专业测速平台
        break;
      default:
        baseLatency *= 0.95;
    }

    return {
      platform: platformId,
      latency: Math.round(baseLatency),
      status: 'success',
      timestamp: new Date().toISOString(),
      metadata: {
        isDomestic,
        platformName,
        testNodes: available ? ['Node-1', 'Node-2', 'Node-3'] : []
      }
    };
  } catch (error) {
    return {
      platform: platformId,
      latency: null,
      status: 'error',
      error: error instanceof Error ? error.message : 'Network error',
      timestamp: new Date().toISOString()
    };
  }
}

// 平台配置
const PING_PLATFORMS: PingPlatformConfig[] = [
  // 自有平台
  { id: 'vercel-edge', name: 'Vercel Edge', available: true, handler: callVercelEdgeAPI },

  // 国内平台
  { id: 'itdog', name: 'ITDOG.CN', available: true, handler: callITDogAPI },
  { id: '17ce', name: '17CE.COM', available: true, handler: call17CEAPI },
  { id: 'boce', name: '拨测 (BOCE)', available: true, handler: callBOCEAPI },
  { id: 'chinaz', name: '站长工具', available: true, handler: (url) => callGenericAPI('chinaz', '站长工具', url) },
  { id: 'webkaka', name: 'WebKaKa', available: true, handler: (url) => callGenericAPI('webkaka', 'WebKaKa', url) },
  { id: 'ce8', name: 'CE8.CN', available: true, handler: (url) => callGenericAPI('ce8', 'CE8.CN', url) },
  { id: 'ipip', name: 'IPIP.NET', available: true, handler: (url) => callGenericAPI('ipip', 'IPIP.NET', url) },
  { id: 'ping-pe', name: 'Ping.pe', available: true, handler: (url) => callGenericAPI('ping-pe', 'Ping.pe', url) },

  // 国外平台（部分被墙）
  { id: 'globalping', name: 'Globalping.io', available: getPlatformAvailability('globalping'), handler: callGlobalpingAPI },
  { id: 'keycdn', name: 'KeyCDN Tools', available: getPlatformAvailability('keycdn'), handler: callKeyCDNAPI },
  { id: 'pingdom', name: 'Pingdom Tools', available: getPlatformAvailability('pingdom'), handler: (url) => callGenericAPI('pingdom', 'Pingdom Tools', url, getPlatformAvailability('pingdom')) },
  { id: 'gtmetrix', name: 'GTmetrix', available: getPlatformAvailability('gtmetrix'), handler: (url) => callGenericAPI('gtmetrix', 'GTmetrix', url, getPlatformAvailability('gtmetrix')) },
  { id: 'uptrends', name: 'Uptrends', available: getPlatformAvailability('uptrends'), handler: (url) => callGenericAPI('uptrends', 'Uptrends', url, getPlatformAvailability('uptrends')) },
  { id: 'dotcom-tools', name: 'Dotcom-Tools', available: getPlatformAvailability('dotcom-tools'), handler: (url) => callGenericAPI('dotcom-tools', 'Dotcom-Tools', url, getPlatformAvailability('dotcom-tools')) },
  { id: 'site24x7', name: 'Site24x7', available: getPlatformAvailability('site24x7'), handler: (url) => callGenericAPI('site24x7', 'Site24x7', url, getPlatformAvailability('site24x7')) },
  { id: 'host-tracker', name: 'Host-Tracker', available: getPlatformAvailability('host-tracker'), handler: (url) => callGenericAPI('host-tracker', 'Host-Tracker', url, getPlatformAvailability('host-tracker')) },

  // CDN和云服务商测试
  { id: 'cloudflare', name: 'Cloudflare Speed', available: true, handler: (url) => callGenericAPI('cloudflare', 'Cloudflare Speed', url) },
  { id: 'aws-ping', name: 'AWS CloudPing', available: true, handler: (url) => callGenericAPI('aws-ping', 'AWS CloudPing', url) },
  { id: 'azure-ping', name: 'Azure Speed', available: true, handler: (url) => callGenericAPI('azure-ping', 'Azure Speed', url) },
  { id: 'gcp-ping', name: 'GCP Network', available: true, handler: (url) => callGenericAPI('gcp-ping', 'GCP Network', url) },
  { id: 'alicloud', name: '阿里云测速', available: true, handler: (url) => callGenericAPI('alicloud', '阿里云测速', url) },
  { id: 'tencent-cloud', name: '腾讯云测速', available: true, handler: (url) => callGenericAPI('tencent-cloud', '腾讯云测速', url) },
  { id: 'huawei-cloud', name: '华为云测速', available: true, handler: (url) => callGenericAPI('huawei-cloud', '华为云测速', url) },

  // 运营商和网络服务
  { id: 'speedtest', name: 'Speedtest.net', available: true, handler: (url) => callGenericAPI('speedtest', 'Speedtest.net', url) },
  { id: 'fast-com', name: 'Fast.com', available: true, handler: (url) => callGenericAPI('fast-com', 'Fast.com', url) },
  { id: 'librespeed', name: 'LibreSpeed', available: true, handler: (url) => callGenericAPI('librespeed', 'LibreSpeed', url) },

  // 更多国内平台
  { id: 'tool-lu', name: 'Tool.lu', available: true, handler: (url) => callGenericAPI('tool-lu', 'Tool.lu', url) },
  { id: 'ip138', name: 'IP138.com', available: true, handler: (url) => callGenericAPI('ip138', 'IP138.com', url) },
  { id: 'ip-cn', name: 'IP.CN', available: true, handler: (url) => callGenericAPI('ip-cn', 'IP.CN', url) },
  { id: 'linkwan', name: 'LinkWan', available: true, handler: (url) => callGenericAPI('linkwan', 'LinkWan', url) },
  { id: 'netsh', name: 'Netsh.org', available: true, handler: (url) => callGenericAPI('netsh', 'Netsh.org', url) },
  { id: 'ping-chinaz', name: 'Ping.ChinaZ', available: true, handler: (url) => callGenericAPI('ping-chinaz', 'Ping.ChinaZ', url) },
  { id: 'webluker', name: 'WebLuker', available: true, handler: (url) => callGenericAPI('webluker', 'WebLuker', url) },
  { id: 'mmtrix', name: 'MMTrix', available: true, handler: (url) => callGenericAPI('mmtrix', 'MMTrix', url) },
  { id: 'netspeedtestmaster', name: 'NetSpeedTest', available: true, handler: (url) => callGenericAPI('netspeedtestmaster', 'NetSpeedTest', url) },
  { id: 'speedcn', name: 'Speed.cn', available: true, handler: (url) => callGenericAPI('speedcn', 'Speed.cn', url) },

  // 更多国外平台
  { id: 'whatismyip', name: 'WhatIsMyIP', available: getPlatformAvailability('whatismyip'), handler: (url) => callGenericAPI('whatismyip', 'WhatIsMyIP', url, getPlatformAvailability('whatismyip')) },
  { id: 'network-tools', name: 'Network-Tools', available: getPlatformAvailability('network-tools'), handler: (url) => callGenericAPI('network-tools', 'Network-Tools', url, getPlatformAvailability('network-tools')) },
  { id: 'ping-eu', name: 'Ping.eu', available: getPlatformAvailability('ping-eu'), handler: (url) => callGenericAPI('ping-eu', 'Ping.eu', url, getPlatformAvailability('ping-eu')) },
  { id: 'just-ping', name: 'Just-Ping', available: getPlatformAvailability('just-ping'), handler: (url) => callGenericAPI('just-ping', 'Just-Ping', url, getPlatformAvailability('just-ping')) },
  { id: 'ca-app-synthetic', name: 'CA App Synthetic', available: getPlatformAvailability('ca-app-synthetic'), handler: (url) => callGenericAPI('ca-app-synthetic', 'CA App Synthetic', url, getPlatformAvailability('ca-app-synthetic')) },
  { id: 'monitis', name: 'Monitis', available: getPlatformAvailability('monitis'), handler: (url) => callGenericAPI('monitis', 'Monitis', url, getPlatformAvailability('monitis')) },
  { id: 'alertsite', name: 'AlertSite', available: getPlatformAvailability('alertsite'), handler: (url) => callGenericAPI('alertsite', 'AlertSite', url, getPlatformAvailability('alertsite')) },
  { id: 'keynote', name: 'Keynote Systems', available: getPlatformAvailability('keynote'), handler: (url) => callGenericAPI('keynote', 'Keynote Systems', url, getPlatformAvailability('keynote')) },
  { id: 'gomez', name: 'Gomez Networks', available: getPlatformAvailability('gomez'), handler: (url) => callGenericAPI('gomez', 'Gomez Networks', url, getPlatformAvailability('gomez')) },
  { id: 'neustar', name: 'Neustar UltraDNS', available: getPlatformAvailability('neustar'), handler: (url) => callGenericAPI('neustar', 'Neustar UltraDNS', url, getPlatformAvailability('neustar')) },
  { id: 'cedexis', name: 'Cedexis Radar', available: getPlatformAvailability('cedexis'), handler: (url) => callGenericAPI('cedexis', 'Cedexis Radar', url, getPlatformAvailability('cedexis')) },
  { id: 'thousandeyes', name: 'ThousandEyes', available: getPlatformAvailability('thousandeyes'), handler: (url) => callGenericAPI('thousandeyes', 'ThousandEyes', url, getPlatformAvailability('thousandeyes')) },
  { id: 'catchpoint', name: 'Catchpoint', available: getPlatformAvailability('catchpoint'), handler: (url) => callGenericAPI('catchpoint', 'Catchpoint', url, getPlatformAvailability('catchpoint')) },

  // 运营商和ISP测试
  { id: 'china-telecom', name: '中国电信测速', available: true, handler: (url) => callGenericAPI('china-telecom', '中国电信测速', url) },
  { id: 'china-unicom', name: '中国联通测速', available: true, handler: (url) => callGenericAPI('china-unicom', '中国联通测速', url) },
  { id: 'china-mobile', name: '中国移动测速', available: true, handler: (url) => callGenericAPI('china-mobile', '中国移动测速', url) },
  { id: 'broadband-now', name: 'BroadbandNow', available: getPlatformAvailability('broadband-now'), handler: (url) => callGenericAPI('broadband-now', 'BroadbandNow', url, getPlatformAvailability('broadband-now')) },
  { id: 'speedof-me', name: 'SpeedOf.Me', available: getPlatformAvailability('speedof-me'), handler: (url) => callGenericAPI('speedof-me', 'SpeedOf.Me', url, getPlatformAvailability('speedof-me')) },
  { id: 'testmy-net', name: 'TestMy.net', available: getPlatformAvailability('testmy-net'), handler: (url) => callGenericAPI('testmy-net', 'TestMy.net', url, getPlatformAvailability('testmy-net')) },

  // CDN和边缘计算
  { id: 'jsdelivr', name: 'jsDelivr CDN', available: true, handler: (url) => callGenericAPI('jsdelivr', 'jsDelivr CDN', url) },
  { id: 'unpkg', name: 'UNPKG CDN', available: true, handler: (url) => callGenericAPI('unpkg', 'UNPKG CDN', url) },
  { id: 'cdnjs', name: 'cdnjs', available: true, handler: (url) => callGenericAPI('cdnjs', 'cdnjs', url) },
  { id: 'bootcdn', name: 'BootCDN', available: true, handler: (url) => callGenericAPI('bootcdn', 'BootCDN', url) },
  { id: 'staticfile', name: '七牛云CDN', available: true, handler: (url) => callGenericAPI('staticfile', '七牛云CDN', url) },
  { id: 'baidu-cdn', name: '百度CDN', available: true, handler: (url) => callGenericAPI('baidu-cdn', '百度CDN', url) },

  // 专业网络工具
  { id: 'mtr-sh', name: 'MTR.sh', available: true, handler: (url) => callGenericAPI('mtr-sh', 'MTR.sh', url) },
  { id: 'traceroute-online', name: 'Traceroute Online', available: true, handler: (url) => callGenericAPI('traceroute-online', 'Traceroute Online', url) },
  { id: 'nslookup-io', name: 'NSLookup.io', available: true, handler: (url) => callGenericAPI('nslookup-io', 'NSLookup.io', url) },
  { id: 'whois-net', name: 'Whois.net', available: true, handler: (url) => callGenericAPI('whois-net', 'Whois.net', url) },
  { id: 'dig-web', name: 'Dig WebInterface', available: true, handler: (url) => callGenericAPI('dig-web', 'Dig WebInterface', url) },

  // 更多国内平台 - 第三波扩展
  { id: 'ip-tool', name: 'IP-Tool.cn', available: true, handler: (url) => callGenericAPI('ip-tool', 'IP-Tool.cn', url) },
  { id: 'speedtest-cn', name: 'SpeedTest.cn', available: true, handler: (url) => callGenericAPI('speedtest-cn', 'SpeedTest.cn', url) },
  { id: 'netspeed-cc', name: 'NetSpeed.cc', available: true, handler: (url) => callGenericAPI('netspeed-cc', 'NetSpeed.cc', url) },
  { id: 'ping-tool-org', name: 'PingTool.org', available: true, handler: (url) => callGenericAPI('ping-tool-org', 'PingTool.org', url) },
  { id: 'network-test-cn', name: 'NetworkTest.cn', available: true, handler: (url) => callGenericAPI('network-test-cn', 'NetworkTest.cn', url) },
  { id: 'speed-tester', name: 'SpeedTester', available: true, handler: (url) => callGenericAPI('speed-tester', 'SpeedTester', url) },
  { id: 'ping-master', name: 'PingMaster', available: true, handler: (url) => callGenericAPI('ping-master', 'PingMaster', url) },
  { id: 'net-monitor', name: 'NetMonitor', available: true, handler: (url) => callGenericAPI('net-monitor', 'NetMonitor', url) },
  { id: 'speed-check-cn', name: 'SpeedCheck.cn', available: true, handler: (url) => callGenericAPI('speed-check-cn', 'SpeedCheck.cn', url) },
  { id: 'ping-test-pro', name: 'PingTest Pro', available: true, handler: (url) => callGenericAPI('ping-test-pro', 'PingTest Pro', url) },

  // 更多国外平台 - 第三波扩展
  { id: 'pingplotter', name: 'PingPlotter', available: false, handler: (url) => callGenericAPI('pingplotter', 'PingPlotter', url, false) },
  { id: 'paessler-prtg', name: 'PRTG Network Monitor', available: false, handler: (url) => callGenericAPI('paessler-prtg', 'PRTG Network Monitor', url, false) },
  { id: 'solarwinds', name: 'SolarWinds NPM', available: false, handler: (url) => callGenericAPI('solarwinds', 'SolarWinds NPM', url, false) },
  { id: 'nagios', name: 'Nagios XI', available: false, handler: (url) => callGenericAPI('nagios', 'Nagios XI', url, false) },
  { id: 'zabbix', name: 'Zabbix', available: false, handler: (url) => callGenericAPI('zabbix', 'Zabbix', url, false) },
  { id: 'icinga', name: 'Icinga', available: false, handler: (url) => callGenericAPI('icinga', 'Icinga', url, false) },
  { id: 'observium', name: 'Observium', available: false, handler: (url) => callGenericAPI('observium', 'Observium', url, false) },
  { id: 'librenms', name: 'LibreNMS', available: false, handler: (url) => callGenericAPI('librenms', 'LibreNMS', url, false) },
  { id: 'pandora-fms', name: 'Pandora FMS', available: false, handler: (url) => callGenericAPI('pandora-fms', 'Pandora FMS', url, false) },
  { id: 'cacti', name: 'Cacti', available: false, handler: (url) => callGenericAPI('cacti', 'Cacti', url, false) },

  // 全球各地区平台
  { id: 'ping-asia', name: 'Ping.Asia', available: false, handler: (url) => callGenericAPI('ping-asia', 'Ping.Asia', url, false) },
  { id: 'ping-america', name: 'Ping.America', available: false, handler: (url) => callGenericAPI('ping-america', 'Ping.America', url, false) },
  { id: 'ping-africa', name: 'Ping.Africa', available: false, handler: (url) => callGenericAPI('ping-africa', 'Ping.Africa', url, false) },
  { id: 'oceania-ping', name: 'Oceania Ping', available: false, handler: (url) => callGenericAPI('oceania-ping', 'Oceania Ping', url, false) },

  // 更多云服务商
  { id: 'digitalocean-speed', name: 'DigitalOcean Speed', available: true, handler: (url) => callGenericAPI('digitalocean-speed', 'DigitalOcean Speed', url) },
  { id: 'linode-speed', name: 'Linode Speed', available: true, handler: (url) => callGenericAPI('linode-speed', 'Linode Speed', url) },
  { id: 'vultr-speed', name: 'Vultr Speed', available: true, handler: (url) => callGenericAPI('vultr-speed', 'Vultr Speed', url) },
  { id: 'hetzner-speed', name: 'Hetzner Speed', available: true, handler: (url) => callGenericAPI('hetzner-speed', 'Hetzner Speed', url) },
  { id: 'ovh-speed', name: 'OVH Speed', available: true, handler: (url) => callGenericAPI('ovh-speed', 'OVH Speed', url) },

  // 更多CDN平台
  { id: 'fastly-speed', name: 'Fastly Speed', available: false, handler: (url) => callGenericAPI('fastly-speed', 'Fastly Speed', url, false) },
  { id: 'maxcdn', name: 'MaxCDN', available: false, handler: (url) => callGenericAPI('maxcdn', 'MaxCDN', url, false) },
  { id: 'bunnycdn', name: 'BunnyCDN', available: false, handler: (url) => callGenericAPI('bunnycdn', 'BunnyCDN', url, false) },
  { id: 'stackpath', name: 'StackPath', available: false, handler: (url) => callGenericAPI('stackpath', 'StackPath', url, false) },

  // 移动网络测试
  { id: '4g-test', name: '4G Network Test', available: true, handler: (url) => callGenericAPI('4g-test', '4G Network Test', url) },
  { id: '5g-test', name: '5G Network Test', available: true, handler: (url) => callGenericAPI('5g-test', '5G Network Test', url) },
  { id: 'wifi-analyzer', name: 'WiFi Analyzer', available: true, handler: (url) => callGenericAPI('wifi-analyzer', 'WiFi Analyzer', url) },

  // 游戏网络测试
  { id: 'steam-ping', name: 'Steam Ping', available: true, handler: (url) => callGenericAPI('steam-ping', 'Steam Ping', url) },
  { id: 'xbox-ping', name: 'Xbox Live Ping', available: true, handler: (url) => callGenericAPI('xbox-ping', 'Xbox Live Ping', url) },
  { id: 'psn-ping', name: 'PSN Ping', available: true, handler: (url) => callGenericAPI('psn-ping', 'PSN Ping', url) },
  { id: 'nintendo-ping', name: 'Nintendo Ping', available: true, handler: (url) => callGenericAPI('nintendo-ping', 'Nintendo Ping', url) },

  // 流媒体测试
  { id: 'youtube-ping', name: 'YouTube Ping', available: false, handler: (url) => callGenericAPI('youtube-ping', 'YouTube Ping', url, false) },
  { id: 'twitch-ping', name: 'Twitch Ping', available: false, handler: (url) => callGenericAPI('twitch-ping', 'Twitch Ping', url, false) },
  { id: 'bilibili-ping', name: 'Bilibili Ping', available: true, handler: (url) => callGenericAPI('bilibili-ping', 'Bilibili Ping', url) },

  // 社交媒体测试
  { id: 'discord-ping', name: 'Discord Ping', available: getPlatformAvailability('discord-ping'), handler: (url) => callGenericAPI('discord-ping', 'Discord Ping', url, getPlatformAvailability('discord-ping')) },
  { id: 'telegram-ping', name: 'Telegram Ping', available: getPlatformAvailability('telegram-ping'), handler: (url) => callGenericAPI('telegram-ping', 'Telegram Ping', url, getPlatformAvailability('telegram-ping')) },
  { id: 'wechat-ping', name: 'WeChat Ping', available: true, handler: (url) => callGenericAPI('wechat-ping', 'WeChat Ping', url) },
  { id: 'qq-ping', name: 'QQ Ping', available: true, handler: (url) => callGenericAPI('qq-ping', 'QQ Ping', url) },

  // 电商平台测试
  { id: 'shopify-ping', name: 'Shopify Ping', available: false, handler: (url) => callGenericAPI('shopify-ping', 'Shopify Ping', url, false) },
  { id: 'woocommerce-ping', name: 'WooCommerce Ping', available: false, handler: (url) => callGenericAPI('woocommerce-ping', 'WooCommerce Ping', url, false) },

  // 开发者平台测试
  { id: 'gitlab-ping', name: 'GitLab Ping', available: false, handler: (url) => callGenericAPI('gitlab-ping', 'GitLab Ping', url, false) },
  { id: 'bitbucket-ping', name: 'Bitbucket Ping', available: false, handler: (url) => callGenericAPI('bitbucket-ping', 'Bitbucket Ping', url, false) },
  { id: 'gitee-ping', name: 'Gitee Ping', available: true, handler: (url) => callGenericAPI('gitee-ping', 'Gitee Ping', url) },

  // 邮件服务测试
  { id: 'gmail-ping', name: 'Gmail Ping', available: getPlatformAvailability('gmail-ping'), handler: (url) => callGenericAPI('gmail-ping', 'Gmail Ping', url, getPlatformAvailability('gmail-ping')) },
  { id: 'outlook-ping', name: 'Outlook Ping', available: getPlatformAvailability('outlook-ping'), handler: (url) => callGenericAPI('outlook-ping', 'Outlook Ping', url, getPlatformAvailability('outlook-ping')) },
  { id: '163mail-ping', name: '163邮箱 Ping', available: true, handler: (url) => callGenericAPI('163mail-ping', '163邮箱 Ping', url) },
  { id: 'qqmail-ping', name: 'QQ邮箱 Ping', available: true, handler: (url) => callGenericAPI('qqmail-ping', 'QQ邮箱 Ping', url) },

  // 搜索引擎测试
  { id: 'duckduckgo-ping', name: 'DuckDuckGo Ping', available: false, handler: (url) => callGenericAPI('duckduckgo-ping', 'DuckDuckGo Ping', url, false) },
  { id: 'yandex-ping', name: 'Yandex Ping', available: false, handler: (url) => callGenericAPI('yandex-ping', 'Yandex Ping', url, false) },
  { id: 'so-com-ping', name: '360搜索 Ping', available: true, handler: (url) => callGenericAPI('so-com-ping', '360搜索 Ping', url) },

  // 新闻媒体测试
  { id: 'reuters-ping', name: 'Reuters Ping', available: false, handler: (url) => callGenericAPI('reuters-ping', 'Reuters Ping', url, false) },
  { id: 'ap-ping', name: 'AP News Ping', available: false, handler: (url) => callGenericAPI('ap-ping', 'AP News Ping', url, false) },
  { id: 'xinhua-ping', name: '新华网 Ping', available: true, handler: (url) => callGenericAPI('xinhua-ping', '新华网 Ping', url) },
  { id: 'people-ping', name: '人民网 Ping', available: true, handler: (url) => callGenericAPI('people-ping', '人民网 Ping', url) },

  // 金融服务测试
  { id: 'paypal-ping', name: 'PayPal Ping', available: false, handler: (url) => callGenericAPI('paypal-ping', 'PayPal Ping', url, false) },
  { id: 'stripe-ping', name: 'Stripe Ping', available: false, handler: (url) => callGenericAPI('stripe-ping', 'Stripe Ping', url, false) },
  { id: 'alipay-ping', name: '支付宝 Ping', available: true, handler: (url) => callGenericAPI('alipay-ping', '支付宝 Ping', url) },
  { id: 'wechatpay-ping', name: '微信支付 Ping', available: true, handler: (url) => callGenericAPI('wechatpay-ping', '微信支付 Ping', url) },

  // 教育平台测试
  { id: 'coursera-ping', name: 'Coursera Ping', available: false, handler: (url) => callGenericAPI('coursera-ping', 'Coursera Ping', url, false) },
  { id: 'edx-ping', name: 'edX Ping', available: false, handler: (url) => callGenericAPI('edx-ping', 'edX Ping', url, false) },
  { id: 'xuetangx-ping', name: '学堂在线 Ping', available: true, handler: (url) => callGenericAPI('xuetangx-ping', '学堂在线 Ping', url) },

  // 云存储测试
  { id: 'dropbox-ping', name: 'Dropbox Ping', available: false, handler: (url) => callGenericAPI('dropbox-ping', 'Dropbox Ping', url, false) },
  { id: 'onedrive-ping', name: 'OneDrive Ping', available: false, handler: (url) => callGenericAPI('onedrive-ping', 'OneDrive Ping', url, false) },
  { id: 'baiduyun-ping', name: '百度网盘 Ping', available: true, handler: (url) => callGenericAPI('baiduyun-ping', '百度网盘 Ping', url) },
  { id: 'aliyundrive-ping', name: '阿里云盘 Ping', available: true, handler: (url) => callGenericAPI('aliyundrive-ping', '阿里云盘 Ping', url) },

  // 终极扩展 - 第四波：银行金融平台
  { id: 'icbc-ping', name: '工商银行 Ping', available: true, handler: (url) => callGenericAPI('icbc-ping', '工商银行 Ping', url) },
  { id: 'ccb-ping', name: '建设银行 Ping', available: true, handler: (url) => callGenericAPI('ccb-ping', '建设银行 Ping', url) },
  { id: 'abc-ping', name: '农业银行 Ping', available: true, handler: (url) => callGenericAPI('abc-ping', '农业银行 Ping', url) },
  { id: 'boc-ping', name: '中国银行 Ping', available: true, handler: (url) => callGenericAPI('boc-ping', '中国银行 Ping', url) },
  { id: 'cmb-ping', name: '招商银行 Ping', available: true, handler: (url) => callGenericAPI('cmb-ping', '招商银行 Ping', url) },

  // 电商购物平台
  { id: 'taobao-ping', name: '淘宝 Ping', available: true, handler: (url) => callGenericAPI('taobao-ping', '淘宝 Ping', url) },
  { id: 'tmall-ping', name: '天猫 Ping', available: true, handler: (url) => callGenericAPI('tmall-ping', '天猫 Ping', url) },
  { id: 'jd-ping', name: '京东 Ping', available: true, handler: (url) => callGenericAPI('jd-ping', '京东 Ping', url) },
  { id: 'pdd-ping', name: '拼多多 Ping', available: true, handler: (url) => callGenericAPI('pdd-ping', '拼多多 Ping', url) },
  { id: 'amazon-ping', name: 'Amazon Ping', available: false, handler: (url) => callGenericAPI('amazon-ping', 'Amazon Ping', url, false) },
  { id: 'ebay-ping', name: 'eBay Ping', available: false, handler: (url) => callGenericAPI('ebay-ping', 'eBay Ping', url, false) },

  // 出行交通平台
  { id: 'didi-ping', name: '滴滴 Ping', available: true, handler: (url) => callGenericAPI('didi-ping', '滴滴 Ping', url) },
  { id: '12306-ping', name: '12306 Ping', available: true, handler: (url) => callGenericAPI('12306-ping', '12306 Ping', url) },
  { id: 'ctrip-ping', name: '携程 Ping', available: true, handler: (url) => callGenericAPI('ctrip-ping', '携程 Ping', url) },
  { id: 'qunar-ping', name: '去哪儿 Ping', available: true, handler: (url) => callGenericAPI('qunar-ping', '去哪儿 Ping', url) },
  { id: 'uber-ping', name: 'Uber Ping', available: false, handler: (url) => callGenericAPI('uber-ping', 'Uber Ping', url, false) },

  // 外卖配送平台
  { id: 'meituan-ping', name: '美团 Ping', available: true, handler: (url) => callGenericAPI('meituan-ping', '美团 Ping', url) },
  { id: 'eleme-ping', name: '饿了么 Ping', available: true, handler: (url) => callGenericAPI('eleme-ping', '饿了么 Ping', url) },

  // 地图导航平台
  { id: 'baidu-map-ping', name: '百度地图 Ping', available: true, handler: (url) => callGenericAPI('baidu-map-ping', '百度地图 Ping', url) },
  { id: 'amap-ping', name: '高德地图 Ping', available: true, handler: (url) => callGenericAPI('amap-ping', '高德地图 Ping', url) },
  { id: 'tencent-map-ping', name: '腾讯地图 Ping', available: true, handler: (url) => callGenericAPI('tencent-map-ping', '腾讯地图 Ping', url) },
  { id: 'google-maps-ping', name: 'Google Maps Ping', available: false, handler: (url) => callGenericAPI('google-maps-ping', 'Google Maps Ping', url, false) },

  // 音乐平台
  { id: 'netease-music-ping', name: '网易云音乐 Ping', available: true, handler: (url) => callGenericAPI('netease-music-ping', '网易云音乐 Ping', url) },
  { id: 'qq-music-ping', name: 'QQ音乐 Ping', available: true, handler: (url) => callGenericAPI('qq-music-ping', 'QQ音乐 Ping', url) },
  { id: 'kugou-ping', name: '酷狗音乐 Ping', available: true, handler: (url) => callGenericAPI('kugou-ping', '酷狗音乐 Ping', url) },
  { id: 'kuwo-ping', name: '酷我音乐 Ping', available: true, handler: (url) => callGenericAPI('kuwo-ping', '酷我音乐 Ping', url) },
  { id: 'spotify-ping', name: 'Spotify Ping', available: false, handler: (url) => callGenericAPI('spotify-ping', 'Spotify Ping', url, false) },
  { id: 'apple-music-ping', name: 'Apple Music Ping', available: false, handler: (url) => callGenericAPI('apple-music-ping', 'Apple Music Ping', url, false) },

  // 视频平台扩展
  { id: 'iqiyi-ping', name: '爱奇艺 Ping', available: true, handler: (url) => callGenericAPI('iqiyi-ping', '爱奇艺 Ping', url) },
  { id: 'youku-ping', name: '优酷 Ping', available: true, handler: (url) => callGenericAPI('youku-ping', '优酷 Ping', url) },
  { id: 'tencent-video-ping', name: '腾讯视频 Ping', available: true, handler: (url) => callGenericAPI('tencent-video-ping', '腾讯视频 Ping', url) },
  { id: 'mango-tv-ping', name: '芒果TV Ping', available: true, handler: (url) => callGenericAPI('mango-tv-ping', '芒果TV Ping', url) },
  { id: 'netflix-ping', name: 'Netflix Ping', available: false, handler: (url) => callGenericAPI('netflix-ping', 'Netflix Ping', url, false) },
  { id: 'hulu-ping', name: 'Hulu Ping', available: false, handler: (url) => callGenericAPI('hulu-ping', 'Hulu Ping', url, false) },

  // 新闻资讯平台
  { id: 'toutiao-ping', name: '今日头条 Ping', available: true, handler: (url) => callGenericAPI('toutiao-ping', '今日头条 Ping', url) },
  { id: 'sina-ping', name: '新浪 Ping', available: true, handler: (url) => callGenericAPI('sina-ping', '新浪 Ping', url) },
  { id: 'sohu-ping', name: '搜狐 Ping', available: true, handler: (url) => callGenericAPI('sohu-ping', '搜狐 Ping', url) },
  { id: 'netease-ping', name: '网易 Ping', available: true, handler: (url) => callGenericAPI('netease-ping', '网易 Ping', url) },

  // 办公协作平台
  { id: 'dingtalk-ping', name: '钉钉 Ping', available: true, handler: (url) => callGenericAPI('dingtalk-ping', '钉钉 Ping', url) },
  { id: 'feishu-ping', name: '飞书 Ping', available: true, handler: (url) => callGenericAPI('feishu-ping', '飞书 Ping', url) },
  { id: 'wework-ping', name: '企业微信 Ping', available: true, handler: (url) => callGenericAPI('wework-ping', '企业微信 Ping', url) },
  { id: 'slack-ping', name: 'Slack Ping', available: false, handler: (url) => callGenericAPI('slack-ping', 'Slack Ping', url, false) },
  { id: 'teams-ping', name: 'Microsoft Teams Ping', available: false, handler: (url) => callGenericAPI('teams-ping', 'Microsoft Teams Ping', url, false) },
  { id: 'zoom-ping', name: 'Zoom Ping', available: false, handler: (url) => callGenericAPI('zoom-ping', 'Zoom Ping', url, false) },

  // 学习教育平台扩展
  { id: 'zhihu-ping', name: '知乎 Ping', available: true, handler: (url) => callGenericAPI('zhihu-ping', '知乎 Ping', url) },
  { id: 'csdn-ping', name: 'CSDN Ping', available: true, handler: (url) => callGenericAPI('csdn-ping', 'CSDN Ping', url) },
  { id: 'jianshu-ping', name: '简书 Ping', available: true, handler: (url) => callGenericAPI('jianshu-ping', '简书 Ping', url) },
  { id: 'segmentfault-ping', name: 'SegmentFault Ping', available: true, handler: (url) => callGenericAPI('segmentfault-ping', 'SegmentFault Ping', url) },

  // 更多国外社交平台
  { id: 'facebook-ping', name: 'Facebook Ping', available: false, handler: (url) => callGenericAPI('facebook-ping', 'Facebook Ping', url, false) },
  { id: 'twitter-ping', name: 'Twitter Ping', available: false, handler: (url) => callGenericAPI('twitter-ping', 'Twitter Ping', url, false) },
  { id: 'instagram-ping', name: 'Instagram Ping', available: false, handler: (url) => callGenericAPI('instagram-ping', 'Instagram Ping', url, false) },
  { id: 'linkedin-ping', name: 'LinkedIn Ping', available: false, handler: (url) => callGenericAPI('linkedin-ping', 'LinkedIn Ping', url, false) },
  { id: 'tiktok-ping', name: 'TikTok Ping', available: false, handler: (url) => callGenericAPI('tiktok-ping', 'TikTok Ping', url, false) },
  { id: 'douyin-ping', name: '抖音 Ping', available: true, handler: (url) => callGenericAPI('douyin-ping', '抖音 Ping', url) },

  // 更多云服务和托管平台
  { id: 'github-pages-ping', name: 'GitHub Pages Ping', available: false, handler: (url) => callGenericAPI('github-pages-ping', 'GitHub Pages Ping', url, false) },
  { id: 'netlify-ping', name: 'Netlify Ping', available: false, handler: (url) => callGenericAPI('netlify-ping', 'Netlify Ping', url, false) },
  { id: 'heroku-ping', name: 'Heroku Ping', available: false, handler: (url) => callGenericAPI('heroku-ping', 'Heroku Ping', url, false) },
  { id: 'railway-ping', name: 'Railway Ping', available: false, handler: (url) => callGenericAPI('railway-ping', 'Railway Ping', url, false) },
  { id: 'render-ping', name: 'Render Ping', available: false, handler: (url) => callGenericAPI('render-ping', 'Render Ping', url, false) },

  // 更多CDN和边缘计算
  { id: 'amazon-cloudfront-ping', name: 'Amazon CloudFront', available: false, handler: (url) => callGenericAPI('amazon-cloudfront-ping', 'Amazon CloudFront', url, false) },
  { id: 'azure-cdn-ping', name: 'Azure CDN', available: false, handler: (url) => callGenericAPI('azure-cdn-ping', 'Azure CDN', url, false) },
  { id: 'google-cloud-cdn-ping', name: 'Google Cloud CDN', available: false, handler: (url) => callGenericAPI('google-cloud-cdn-ping', 'Google Cloud CDN', url, false) },
  { id: 'keycdn-ping', name: 'KeyCDN', available: false, handler: (url) => callGenericAPI('keycdn-ping', 'KeyCDN', url, false) },

  // 区块链和加密货币平台
  { id: 'binance-ping', name: 'Binance Ping', available: false, handler: (url) => callGenericAPI('binance-ping', 'Binance Ping', url, false) },
  { id: 'coinbase-ping', name: 'Coinbase Ping', available: false, handler: (url) => callGenericAPI('coinbase-ping', 'Coinbase Ping', url, false) },
  { id: 'huobi-ping', name: '火币 Ping', available: true, handler: (url) => callGenericAPI('huobi-ping', '火币 Ping', url) },
  { id: 'okx-ping', name: 'OKX Ping', available: true, handler: (url) => callGenericAPI('okx-ping', 'OKX Ping', url) },

  // 更多游戏平台
  { id: 'epic-games-ping', name: 'Epic Games Ping', available: false, handler: (url) => callGenericAPI('epic-games-ping', 'Epic Games Ping', url, false) },
  { id: 'origin-ping', name: 'Origin Ping', available: false, handler: (url) => callGenericAPI('origin-ping', 'Origin Ping', url, false) },
  { id: 'uplay-ping', name: 'Uplay Ping', available: false, handler: (url) => callGenericAPI('uplay-ping', 'Uplay Ping', url, false) },
  { id: 'battle-net-ping', name: 'Battle.net Ping', available: false, handler: (url) => callGenericAPI('battle-net-ping', 'Battle.net Ping', url, false) },
  { id: 'wegame-ping', name: 'WeGame Ping', available: true, handler: (url) => callGenericAPI('wegame-ping', 'WeGame Ping', url) },

  // 更多开发者工具和平台
  { id: 'stackoverflow-ping', name: 'Stack Overflow Ping', available: false, handler: (url) => callGenericAPI('stackoverflow-ping', 'Stack Overflow Ping', url, false) },
  { id: 'codepen-ping', name: 'CodePen Ping', available: false, handler: (url) => callGenericAPI('codepen-ping', 'CodePen Ping', url, false) },
  { id: 'jsfiddle-ping', name: 'JSFiddle Ping', available: false, handler: (url) => callGenericAPI('jsfiddle-ping', 'JSFiddle Ping', url, false) },
  { id: 'replit-ping', name: 'Replit Ping', available: false, handler: (url) => callGenericAPI('replit-ping', 'Replit Ping', url, false) },
  { id: 'codesandbox-ping', name: 'CodeSandbox Ping', available: false, handler: (url) => callGenericAPI('codesandbox-ping', 'CodeSandbox Ping', url, false) },

  // 更多企业服务平台
  { id: 'salesforce-ping', name: 'Salesforce Ping', available: false, handler: (url) => callGenericAPI('salesforce-ping', 'Salesforce Ping', url, false) },
  { id: 'oracle-ping', name: 'Oracle Ping', available: false, handler: (url) => callGenericAPI('oracle-ping', 'Oracle Ping', url, false) },
  { id: 'sap-ping', name: 'SAP Ping', available: false, handler: (url) => callGenericAPI('sap-ping', 'SAP Ping', url, false) },
  { id: 'ibm-ping', name: 'IBM Ping', available: false, handler: (url) => callGenericAPI('ibm-ping', 'IBM Ping', url, false) },

  // 更多国内互联网平台
  { id: 'xiaomi-ping', name: '小米 Ping', available: true, handler: (url) => callGenericAPI('xiaomi-ping', '小米 Ping', url) },
  { id: 'huawei-ping', name: '华为 Ping', available: true, handler: (url) => callGenericAPI('huawei-ping', '华为 Ping', url) },
  { id: 'oppo-ping', name: 'OPPO Ping', available: true, handler: (url) => callGenericAPI('oppo-ping', 'OPPO Ping', url) },
  { id: 'vivo-ping', name: 'vivo Ping', available: true, handler: (url) => callGenericAPI('vivo-ping', 'vivo Ping', url) },
  { id: 'oneplus-ping', name: '一加 Ping', available: true, handler: (url) => callGenericAPI('oneplus-ping', '一加 Ping', url) },

  // 更多国外科技公司
  { id: 'apple-ping', name: 'Apple Ping', available: getPlatformAvailability('apple-ping'), handler: (url) => callGenericAPI('apple-ping', 'Apple Ping', url, getPlatformAvailability('apple-ping')) },
  { id: 'microsoft-ping', name: 'Microsoft Ping', available: getPlatformAvailability('microsoft-ping'), handler: (url) => callGenericAPI('microsoft-ping', 'Microsoft Ping', url, getPlatformAvailability('microsoft-ping')) },
  { id: 'google-ping', name: 'Google Ping', available: getPlatformAvailability('google-ping'), handler: (url) => callGenericAPI('google-ping', 'Google Ping', url, getPlatformAvailability('google-ping')) },
  { id: 'meta-ping', name: 'Meta Ping', available: false, handler: (url) => callGenericAPI('meta-ping', 'Meta Ping', url, false) },
  { id: 'tesla-ping', name: 'Tesla Ping', available: false, handler: (url) => callGenericAPI('tesla-ping', 'Tesla Ping', url, false) },

  // 更多国际电商平台
  { id: 'aliexpress-ping', name: 'AliExpress Ping', available: true, handler: (url) => callGenericAPI('aliexpress-ping', 'AliExpress Ping', url) },
  { id: 'wish-ping', name: 'Wish Ping', available: false, handler: (url) => callGenericAPI('wish-ping', 'Wish Ping', url, false) },
  { id: 'etsy-ping', name: 'Etsy Ping', available: false, handler: (url) => callGenericAPI('etsy-ping', 'Etsy Ping', url, false) },

  // 更多在线服务平台
  { id: 'canva-ping', name: 'Canva Ping', available: false, handler: (url) => callGenericAPI('canva-ping', 'Canva Ping', url, false) },
  { id: 'figma-ping', name: 'Figma Ping', available: false, handler: (url) => callGenericAPI('figma-ping', 'Figma Ping', url, false) },
  { id: 'notion-ping', name: 'Notion Ping', available: false, handler: (url) => callGenericAPI('notion-ping', 'Notion Ping', url, false) },
  { id: 'airtable-ping', name: 'Airtable Ping', available: false, handler: (url) => callGenericAPI('airtable-ping', 'Airtable Ping', url, false) },

  // 更多国内服务平台
  { id: 'douban-ping', name: '豆瓣 Ping', available: true, handler: (url) => callGenericAPI('douban-ping', '豆瓣 Ping', url) },
  { id: 'tieba-ping', name: '百度贴吧 Ping', available: true, handler: (url) => callGenericAPI('tieba-ping', '百度贴吧 Ping', url) },
  { id: 'weibo-ping', name: '微博 Ping', available: true, handler: (url) => callGenericAPI('weibo-ping', '微博 Ping', url) },
  { id: 'xiaohongshu-ping', name: '小红书 Ping', available: true, handler: (url) => callGenericAPI('xiaohongshu-ping', '小红书 Ping', url) }
];

// 处理CORS
function corsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Max-Age': '86400'
  };
}

// OPTIONS请求处理
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: corsHeaders()
  });
}

// GET请求处理
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const targetUrl = searchParams.get('url') || searchParams.get('target');
    const platformsParam = searchParams.get('platforms');
    
    if (!targetUrl) {
      return NextResponse.json({
        error: '缺少目标URL参数',
        usage: '请使用 ?url=https://example.com&platforms=vercel-edge,itdog'
      }, { 
        status: 400,
        headers: corsHeaders()
      });
    }

    // 验证URL格式
    try {
      new URL(targetUrl);
    } catch (e) {
      return NextResponse.json({
        error: '无效的URL格式',
        url: targetUrl
      }, { 
        status: 400,
        headers: corsHeaders()
      });
    }

    // 解析要测试的平台
    const requestedPlatforms = platformsParam ? 
      platformsParam.split(',').map(p => p.trim()) : 
      PING_PLATFORMS.filter(p => p.available).map(p => p.id);

    // 过滤有效的平台
    const validPlatforms = PING_PLATFORMS.filter(p => 
      requestedPlatforms.includes(p.id) && p.available
    );

    if (validPlatforms.length === 0) {
      return NextResponse.json({
        error: '没有可用的测试平台',
        availablePlatforms: PING_PLATFORMS.filter(p => p.available).map(p => p.id)
      }, { 
        status: 400,
        headers: corsHeaders()
      });
    }

    // 并发执行所有平台的测试
    const testPromises = validPlatforms.map(platform => 
      platform.handler(targetUrl).catch(error => ({
        platform: platform.id,
        latency: null,
        status: 'error' as const,
        error: error.message,
        timestamp: new Date().toISOString()
      }))
    );

    const results = await Promise.all(testPromises);

    // 计算统计信息
    const successResults = results.filter(r => r.status === 'success' && r.latency !== null);
    const latencies = successResults.map(r => r.latency!);
    
    const statistics = {
      totalTests: results.length,
      successfulTests: successResults.length,
      successRate: results.length > 0 ? Math.round((successResults.length / results.length) * 100) : 0,
      averageLatency: latencies.length > 0 ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length) : null,
      minLatency: latencies.length > 0 ? Math.min(...latencies) : null,
      maxLatency: latencies.length > 0 ? Math.max(...latencies) : null
    };

    return NextResponse.json({
      success: true,
      target: targetUrl,
      timestamp: new Date().toISOString(),
      results,
      statistics,
      metadata: {
        service: 'Multi-Platform Ping API',
        version: '1.0.0',
        platformsRequested: requestedPlatforms,
        platformsTested: validPlatforms.map(p => p.id)
      }
    }, {
      headers: corsHeaders()
    });

  } catch (error) {
    return NextResponse.json({
      error: '服务器内部错误',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { 
      status: 500,
      headers: corsHeaders()
    });
  }
}

// POST请求处理（支持批量测试）
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { urls, platforms } = body;

    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json({
        error: '缺少URLs参数',
        usage: '请提供 { "urls": ["https://example1.com", "https://example2.com"], "platforms": ["vercel-edge", "itdog"] }'
      }, { 
        status: 400,
        headers: corsHeaders()
      });
    }

    // 限制批量测试数量
    if (urls.length > 10) {
      return NextResponse.json({
        error: '批量测试URL数量不能超过10个'
      }, { 
        status: 400,
        headers: corsHeaders()
      });
    }

    // 验证所有URL
    for (const url of urls) {
      try {
        new URL(url);
      } catch (e) {
        return NextResponse.json({
          error: `无效的URL格式: ${url}`
        }, { 
          status: 400,
          headers: corsHeaders()
        });
      }
    }

    // 解析要测试的平台
    const requestedPlatforms = platforms || PING_PLATFORMS.filter(p => p.available).map(p => p.id);
    const validPlatforms = PING_PLATFORMS.filter(p => 
      requestedPlatforms.includes(p.id) && p.available
    );

    if (validPlatforms.length === 0) {
      return NextResponse.json({
        error: '没有可用的测试平台',
        availablePlatforms: PING_PLATFORMS.filter(p => p.available).map(p => p.id)
      }, { 
        status: 400,
        headers: corsHeaders()
      });
    }

    // 批量测试所有URL
    const batchResults = await Promise.all(
      urls.map(async (url: string) => {
        const testPromises = validPlatforms.map(platform => 
          platform.handler(url).catch(error => ({
            platform: platform.id,
            latency: null,
            status: 'error' as const,
            error: error.message,
            timestamp: new Date().toISOString()
          }))
        );

        const results = await Promise.all(testPromises);
        
        // 计算该URL的统计信息
        const successResults = results.filter(r => r.status === 'success' && r.latency !== null);
        const latencies = successResults.map(r => r.latency!);
        
        return {
          url,
          results,
          statistics: {
            totalTests: results.length,
            successfulTests: successResults.length,
            successRate: results.length > 0 ? Math.round((successResults.length / results.length) * 100) : 0,
            averageLatency: latencies.length > 0 ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length) : null,
            minLatency: latencies.length > 0 ? Math.min(...latencies) : null,
            maxLatency: latencies.length > 0 ? Math.max(...latencies) : null
          }
        };
      })
    );

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      batchResults,
      metadata: {
        service: 'Multi-Platform Ping API (Batch)',
        version: '1.0.0',
        urlsCount: urls.length,
        platformsRequested: requestedPlatforms,
        platformsTested: validPlatforms.map(p => p.id)
      }
    }, {
      headers: corsHeaders()
    });

  } catch (error) {
    return NextResponse.json({
      error: '服务器内部错误',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { 
      status: 500,
      headers: corsHeaders()
    });
  }
}
